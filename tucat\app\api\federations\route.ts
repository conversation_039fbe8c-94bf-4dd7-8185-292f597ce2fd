import { NextResponse } from 'next/server';
import { redis, safeRedisOperation } from '@/lib/upstash';

export async function GET() {
  try {
    // Get all federation keys with proper pattern matching
    const keys = await safeRedisOperation(() => redis.keys('federation:*'));
    if (!Array.isArray(keys)) {
      return NextResponse.json({ federations: [] });
    }

    // Fetch all federation data
    const federationsRaw = await Promise.all(
      keys.map((key) => safeRedisOperation(() => redis.get(key)))
    );

    // Parse federation data and ensure we keep all valid entries
    const federations = federationsRaw
      .map((item) => {
        if (!item) return null;
        if (typeof item === 'string') {
          try {
            return JSON.parse(item);
          } catch {
            return null;
          }
        }
        return item;
      })
      .filter((fed): fed is any => fed !== null && typeof fed === 'object' && 'id' in fed);

    // Helper function to convert youth percentage string to numeric value for sorting
    const getYouthPercentageValue = (percentage: string): number => {
      switch(percentage) {
        case "above-75%": return 87.5;
        case "51-75%": return 63;
        case "26-50%": return 38;
        case "0-25%": return 12.5;
        default: return 0;
      }
    };

    const sectionCount = 11;
    const sectionScoresAll: { [section: number]: { total: number, count: number, min: number, max: number } } = {};

    for (let i = 1; i <= sectionCount; i++) {
      sectionScoresAll[i] = { total: 0, count: 0, min: 10, max: 0 };
    }

    const federationStats = await Promise.all(
      federations.map(async (federation) => {
        // Get all assessments for this federation
        const assessmentKeys = await safeRedisOperation(() => redis.keys(`assessment:${federation.id}:*`));
        const assessmentsRaw = await Promise.all(
          assessmentKeys.map(key => safeRedisOperation(() => redis.get(key)))
        );

        // Parse assessments
        const assessments = assessmentsRaw
          .map((a) => {
            if (!a) return null;
            if (typeof a === 'string') {
              try {
                return JSON.parse(a);
              } catch {
                return null;
              }
            }
            return a;
          })
          .filter((assessment): assessment is any =>
            assessment !== null &&
            typeof assessment === 'object' &&
            'section_number' in assessment
          );

        // ...rest of the scoring logic remains the same...
        // Scoring logic (copied from useAssessment.ts)
        let total = 0;
        let maxPossible = 0;
        let sectionScores: { [section: number]: number } = {};

        assessments.forEach((assessment) => {
          let sectionScore = 0;
          let sectionMaxScore = 0;
          const sn = assessment.section_number;

          if (sn >= 3 && sn <= 11) {
            sectionScores[sn] = 0;
          }

          // Continue with existing scoring logic...
          // Section III
          if (sn === 3) {
            sectionMaxScore = 14;
            if (assessment.vision_mission_status === '4') sectionScore += 3;
            else if (assessment.vision_mission_status === '3') sectionScore += 2;
            else if (assessment.vision_mission_status === '2') sectionScore += 1;
            if (assessment.vision_posted === 'yes') sectionScore += 1;
            if (assessment.vision_in_documents === 'yes') sectionScore += 1;
            if (assessment.vision_for_planning === 'yes') sectionScore += 1;
            if (assessment.decision_making === 'a') sectionScore += 3;
            else if (assessment.decision_making === 'b') sectionScore += 2;
            else if (assessment.decision_making === 'c') sectionScore += 1;
            if (assessment.emerging_issues_handling === 'a') sectionScore += 3;
            else if (assessment.emerging_issues_handling === 'b') sectionScore += 2;
            else if (assessment.emerging_issues_handling === 'c') sectionScore += 1;
            if (assessment.leadership_development_plan === 'a') sectionScore += 2;
            else if (assessment.leadership_development_plan === 'b') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section IV
          if (sn === 4) {
            sectionMaxScore = 4;
            if (assessment.organizational_structure === 'a') sectionScore += 2;
            else if (assessment.organizational_structure === 'b') sectionScore += 1;
            if (assessment.roles_defined === 'a') sectionScore += 2;
            else if (assessment.roles_defined === 'b') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section V
          if (sn === 5) {
            sectionMaxScore = 11;
            if (assessment.management_approach === 'a') sectionScore += 2;
            else if (assessment.management_approach === 'c') sectionScore += 1;
            else if (assessment.management_approach === 'd') sectionScore += 0;
            if (assessment.authority_delegation === 'a') sectionScore += 2;
            else if (assessment.authority_delegation === 'c') sectionScore += 1;
            if (assessment.decision_making_process === '4') sectionScore += 3;
            else if (assessment.decision_making_process === '3') sectionScore += 2;
            else if (assessment.decision_making_process === '2') sectionScore += 1;
            if (assessment.deputy_availability === 'a') sectionScore += 2;
            else if (assessment.deputy_availability === 'b') sectionScore += 1;
            if (assessment.transition_plan === 'a') sectionScore += 2;
            else if (assessment.transition_plan === 'b') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section VI
          if (sn === 6) {
            sectionMaxScore = 9;
            if (assessment.workers_involvement_level === '4') sectionScore += 4;
            else if (assessment.workers_involvement_level === '3') sectionScore += 3;
            else if (assessment.workers_involvement_level === '2') sectionScore += 2;
            else if (assessment.workers_involvement_level === '1') sectionScore += 1;
            if (assessment.involve_program_activities === 'yes') sectionScore += 1;
            if (assessment.involve_leaders_orientation === 'yes') sectionScore += 1;
            if (assessment.solicit_feedback === 'yes') sectionScore += 1;
            if (assessment.regular_interaction === 'yes') sectionScore += 1;
            if (assessment.share_results === 'yes') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section VII
          if (sn === 7) {
            sectionMaxScore = 7;
            if (assessment.cultural_gender_consideration === '4') sectionScore += 3;
            else if (assessment.cultural_gender_consideration === '3') sectionScore += 2;
            else if (assessment.cultural_gender_consideration === '2') sectionScore += 1;
            if (assessment.consider_local_culture === 'yes') sectionScore += 1;
            if (assessment.documented_guidelines === 'yes') sectionScore += 1;
            if (assessment.provide_training === 'yes') sectionScore += 1;
            if (assessment.use_assessment_findings === 'yes') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section VIII
          if (sn === 8) {
            sectionMaxScore = 8;
            if (assessment.representation_effectiveness === '4') sectionScore += 3;
            else if (assessment.representation_effectiveness === '3') sectionScore += 2;
            else if (assessment.representation_effectiveness === '2') sectionScore += 1;
            if (assessment.member_involvement === '4') sectionScore += 3;
            else if (assessment.member_involvement === '3') sectionScore += 2;
            else if (assessment.member_involvement === '2') sectionScore += 1;
            if (assessment.bargaining_strategy === 'a') sectionScore += 2;
            else if (assessment.bargaining_strategy === 'b') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section IX
          if (sn === 9) {
            sectionMaxScore = 11;
            if (assessment.communication_effectiveness === '4') sectionScore += 3;
            else if (assessment.communication_effectiveness === '3') sectionScore += 2;
            else if (assessment.communication_effectiveness === '2') sectionScore += 1;
            if (assessment.member_engagement === '4') sectionScore += 3;
            else if (assessment.member_engagement === '3') sectionScore += 2;
            else if (assessment.member_engagement === '2') sectionScore += 1;
            if (assessment.participation_opportunities === '4') sectionScore += 3;
            else if (assessment.participation_opportunities === '3') sectionScore += 2;
            else if (assessment.participation_opportunities === '2') sectionScore += 1;
            if (assessment.feedback_collection === 'a') sectionScore += 2;
            else if (assessment.feedback_collection === 'b') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section X
          if (sn === 10) {
            sectionMaxScore = 16;
            if (assessment.fee_collection === '4') sectionScore += 3;
            else if (assessment.fee_collection === '3') sectionScore += 2;
            else if (assessment.fee_collection === '2') sectionScore += 1;
            if (assessment.financial_management === '4') sectionScore += 3;
            else if (assessment.financial_management === '3') sectionScore += 2;
            else if (assessment.financial_management === '2') sectionScore += 1;
            if (assessment.financial_planning === '4') sectionScore += 3;
            else if (assessment.financial_planning === '3') sectionScore += 2;
            else if (assessment.financial_planning === '2') sectionScore += 1;
            if (assessment.financial_system_quality === '4') sectionScore += 3;
            else if (assessment.financial_system_quality === '3') sectionScore += 2;
            else if (assessment.financial_system_quality === '2') sectionScore += 1;
            if (assessment.has_cash_system === 'yes') sectionScore += 1;
            if (assessment.uses_accounting_software === 'yes') sectionScore += 1;
            if (assessment.has_chart_accounts === 'yes') sectionScore += 1;
            if (assessment.reconciles_monthly === 'yes') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Section XI
          if (sn === 11) {
            sectionMaxScore = 10;
            if (assessment.audit_system_quality === '4') sectionScore += 3;
            else if (assessment.audit_system_quality === '3') sectionScore += 2;
            else if (assessment.audit_system_quality === '2') sectionScore += 1;
            if (assessment.requires_annual_audit === 'yes') sectionScore += 1;
            if (assessment.regularly_audited === 'yes') sectionScore += 1;
            if (assessment.auditor_selection === 'yes') sectionScore += 1;
            if (assessment.audit_manager === 'yes') sectionScore += 1;
            if (assessment.implements_recommendations === 'yes') sectionScore += 1;
            if (assessment.shares_reports === 'yes') sectionScore += 1;
            if (assessment.report_provides_info === 'yes') sectionScore += 1;
            total += sectionScore;
            maxPossible += sectionMaxScore;
          }
          // Track section-by-section scores
          if (sn >= 3 && sn <= 11) {
            // Convert to normalized score out of 10 for consistent comparison
            const normalizedScore = sectionMaxScore > 0 ? Math.round((sectionScore / sectionMaxScore) * 10 * 100) / 100 : 0;
            sectionScores[sn] = normalizedScore;

            // Update aggregated section stats
            sectionScoresAll[sn].total += normalizedScore;
            sectionScoresAll[sn].count += 1;

            // Update min and max scores
            sectionScoresAll[sn].min = Math.min(sectionScoresAll[sn].min, normalizedScore);
            sectionScoresAll[sn].max = Math.max(sectionScoresAll[sn].max, normalizedScore);
          }
        });

        // Handle youth percentage properly
        const youthPercentage = federation.youth_percentage || "0-25%";

        return {
          ...federation,
          totalScore: total,
          maxScore: maxPossible,
          percentage: maxPossible > 0 ? Math.round((total / maxPossible) * 100) : 0,
          youth_percentage: youthPercentage,
          youthPercentageValue: getYouthPercentageValue(youthPercentage), // Add numeric value for sorting
          sectionScores
        };
      })
    );

    // Sort federations by youth percentage for consistent display
    federationStats.sort((a, b) => b.youthPercentageValue - a.youthPercentageValue);

    // Calculate overall stats
    const totalScores = federationStats.map(f => f.totalScore);
    // We need maxScores for potential future calculations
    const percentages = federationStats.map(f => f.percentage);

    const avgTotalScore = federationStats.length > 0
      ? Math.round(totalScores.reduce((a, b) => a + b, 0) / federationStats.length)
      : 0;
    const avgPercentage = federationStats.length > 0
      ? Math.round(percentages.reduce((a, b) => a + b, 0) / federationStats.length)
      : 0;

    // Calculate section-by-section averages, mins, and maxes
    const avgSectionScores: { [section: number]: number } = {};
    const minSectionScores: { [section: number]: number } = {};
    const maxSectionScores: { [section: number]: number } = {};

    for (let i = 3; i <= 11; i++) {
      // Calculate average scores
      avgSectionScores[i] = sectionScoresAll[i].count > 0
        ? Math.round((sectionScoresAll[i].total / sectionScoresAll[i].count) * 100) / 100
        : 0;

      // Set min scores (default to 0 if no data)
      minSectionScores[i] = sectionScoresAll[i].count > 0
        ? sectionScoresAll[i].min
        : 0;

      // Set max scores (default to 0 if no data)
      maxSectionScores[i] = sectionScoresAll[i].count > 0
        ? sectionScoresAll[i].max
        : 0;
    }

    // Calculate statistics...
    const youthCounts = {
      high: federationStats.filter(f =>
        f.youth_percentage === "above-75%" || f.youth_percentage === "51-75%"
      ).length,
      moderate: federationStats.filter(f =>
        f.youth_percentage === "26-50%"
      ).length,
      low: federationStats.filter(f =>
        f.youth_percentage === "0-25%" || !f.youth_percentage
      ).length
    };

    // Calculate averages...
    const avgYouthPercentage = federationStats.reduce(
      (sum, fed) => sum + fed.youthPercentageValue,
      0
    ) / federationStats.length;

    return NextResponse.json({
      federations: federationStats,
      avgTotalScore: Math.round(avgTotalScore),
      avgPercentage: Math.round(avgPercentage),
      highestScore: Math.max(...totalScores),
      lowestScore: Math.min(...totalScores),
      highestPercentage: Math.max(...percentages),
      lowestPercentage: Math.min(...percentages),
      avgYouthPercentage: Math.round(avgYouthPercentage * 100) / 100,
      avgSectionScores,
      minSectionScores,
      maxSectionScores,
      youthCounts // Add youth distribution counts
    });

  } catch (error) {
    console.error('Error in GET /api/federations:', error);
    return NextResponse.json(
      { error: 'Failed to fetch federations', details: error?.message },
      { status: 500 }
    );
  }
}