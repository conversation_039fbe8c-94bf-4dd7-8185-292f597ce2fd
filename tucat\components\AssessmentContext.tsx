"use client";

import { createContext, useContext, ReactNode } from 'react';
import { useAssessment, AssessmentState } from '@/lib/hooks/useAssessment';

type AssessmentContextType = AssessmentState & ReturnType<typeof useAssessment>;

const AssessmentContext = createContext<AssessmentContextType | undefined>(undefined);

export function AssessmentProvider({ children }: { children: ReactNode }) {
  const assessment = useAssessment();

  return (
    <AssessmentContext.Provider value={assessment}>
      {children}
    </AssessmentContext.Provider>
  );
}

export function useAssessmentContext() {
  const context = useContext(AssessmentContext);
  if (context === undefined) {
    throw new Error('useAssessmentContext must be used within an AssessmentProvider');
  }
  return context;
}