"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle, Save, Info } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { ReactNode } from "react";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

interface QuestionCardProps {
  title: string;
  bengaliTitle: string;
  showAlert?: boolean;
  alertMessage?: string;
  children: ReactNode;
  onSave: () => void;
  isLoading?: boolean;
  isDisabled?: boolean;
  objective?: {
    english: string;
    bengali: string;
  };
}

export function QuestionCard({
  title,
  bengaliTitle,
  showAlert,
  alertMessage,
  children,
  onSave,
  isLoading,
  isDisabled,
  objective,
}: QuestionCardProps) {
  return (
    <div className="space-y-8">
      <Card className="shadow-md transition-all duration-200 hover:shadow-lg">
        <CardHeader className="pb-3">
          <div className="flex justify-center items-center mb-2">
            <CardTitle className="text-2xl font-bold text-center">{title}</CardTitle>
          </div>
          <CardDescription className="text-center text-lg">{bengaliTitle}</CardDescription>
        </CardHeader>

        {showAlert && (
          <CardContent className="pt-0 pb-2">
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                {alertMessage || "Please complete previous sections before proceeding."}
              </AlertDescription>
            </Alert>
          </CardContent>
        )}

        {objective && (
          <CardContent className={cn("pt-0", showAlert ? "pt-0" : "pt-2")}>
            <div className="bg-muted/50 rounded-lg p-4 border border-muted">
              <div className="flex items-start gap-2">
                <Info className="h-5 w-5 text-muted-foreground mt-0.5" />
                <div>
                  <p className="font-medium mb-1">Objective:</p>
                  <p className="text-sm text-muted-foreground">{objective.english}</p>
                  <p className="text-sm text-muted-foreground mt-1">{objective.bengali}</p>
                </div>
              </div>
            </div>
            <Separator className="my-6" />
          </CardContent>
        )}

        <CardContent className={cn("space-y-8", !objective && !showAlert ? "pt-6" : "pt-0")}>
          {children}
        </CardContent>

        <CardFooter className="flex justify-end border-t pt-4 mt-4">
          <Button
            onClick={onSave}
            disabled={isLoading || isDisabled}
            className="flex items-center gap-2"
          >
            <Save className="h-4 w-4" />
            {isLoading ? "Saving..." : "Save Section"}
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}