"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionVI() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    workers_involvement_level: "",
    involve_program_activities: "",
    involve_leaders_orientation: "",
    solicit_feedback: "",
    regular_interaction: "",
    share_results: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 6
    const sectionData = assessments.find(a => a.section_number === 6);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        workers_involvement_level: sectionData.workers_involvement_level || "",
        involve_program_activities: sectionData.involve_program_activities || "",
        involve_leaders_orientation: sectionData.involve_leaders_orientation || "",
        solicit_feedback: sectionData.solicit_feedback || "",
        regular_interaction: sectionData.regular_interaction || "",
        share_results: sectionData.share_results || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please fill out all the questions before saving.");
        return;
      }

      await saveAssessment(6, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  const handleYesNoChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <QuestionCard
      title="WORKERS' INVOLVEMENT"
      bengaliTitle="শ্রমিকদের অংশগ্রহণ"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the level and effectiveness of worker participation in federation activities and decision-making processes.",
        bengali: "ফেডারেশনের কার্যক্রম এবং সিদ্ধান্ত গ্রহণ প্রক্রিয়ায় শ্রমিকদের অংশগ্রহণের মাত্রা এবং কার্যকারিতা মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="What is the level of workers' involvement in the Federation?"
        bengaliQuestion="ফেডারেশনে শ্রমিকদের অংশগ্রহণের মাত্রা কেমন?"
        value={formData.workers_involvement_level}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, workers_involvement_level: value }))
        }
        options={[
          {
            value: "1",
            label: "The Federation orients workers on their rights, but does not actively include them",
            bengaliLabel: "শ্রমিকদের তাদের অধিকার সম্পর্কে ধারণা দেয়, তবে সক্রিয়ভাবে তাদের অন্তর্ভুক্ত করে না।"
          },
          {
            value: "2",
            label: "The Federation orients workers on their rights issues and discusses its approach with relevant leaders",
            bengaliLabel: "শ্রমিকদের তাদের অধিকার সম্পর্কে ধারণা দেয় এবং প্রাসঙ্গিক নেতাদের সাথে এর পদ্ধতি নিয়ে আলোচনা করে।"
          },
          {
            value: "3",
            label: "The Federation orients workers and leaders on their rights issues and actively engages them in the activities",
            bengaliLabel: "শ্রমিকদের এবং নেতাদের তাদের অধিকার সম্পর্কে ধারণা দেয় এবং সক্রিয়ভাবে তাদের কার্যক্রমে সম্পৃক্ত করে।"
          },
          {
            value: "4",
            label: "The Federation orients workers and leaders on their rights issues and actively engages them in activities and service provision",
            bengaliLabel: "শ্রমিকদের এবং নেতাদের তাদের অধিকার সম্পর্কে ধারণা দেয় এবং কার্যক্রম এবং সেবা প্রদানে সক্রিয়ভাবে সম্পৃক্ত করে।"
          }
        ]}
      />

      <div className="border rounded-lg overflow-hidden bg-blue-50/50">
        <div className="p-4 border-b">
          <div className="font-semibold">Workers' Involvement Assessment</div>
          <div className="text-sm">শ্রমিকদের অংশগ্রহণ মূল্যায়ন</div>
        </div>
        <table className="w-full border-collapse">
          <thead className="bg-muted/50">
            <tr className="border-b">
              <th className="p-3 text-left">#</th>
              <th className="p-3 text-left">Question</th>
              <th className="p-3 text-center w-24">Yes</th>
              <th className="p-3 text-center w-24">No</th>
              <th className="p-3 text-center w-24">N/A</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">2.</td>
              <td className="p-3 border-r">
                <div>Does your Federation involve the workers in program activities?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি শ্রমিকদের প্রোগ্রাম কার্যক্রমে অন্তর্ভুক্ত করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="involve_program_activities"
                      value="yes"
                      checked={formData.involve_program_activities === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, involve_program_activities: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="involve_program_activities"
                      value="no"
                      checked={formData.involve_program_activities === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, involve_program_activities: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="involve_program_activities"
                      value="na"
                      checked={formData.involve_program_activities === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, involve_program_activities: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">3.</td>
              <td className="p-3 border-r">
                <div>Does your Federation identify and involve workers' leaders in program orientation, design and review?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি শ্রমিকদের নেতাদের কর্মসূচীতে, নকশায় এবং পর্যালোচনায় অন্তর্ভুক্ত করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="involve_leaders_orientation"
                      value="yes"
                      checked={formData.involve_leaders_orientation === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, involve_leaders_orientation: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="involve_leaders_orientation"
                      value="no"
                      checked={formData.involve_leaders_orientation === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, involve_leaders_orientation: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="involve_leaders_orientation"
                      value="na"
                      checked={formData.involve_leaders_orientation === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, involve_leaders_orientation: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">4.</td>
              <td className="p-3 border-r">
                <div>Does your Federation solicit feedback and information from target groups?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি টার্গেট গোষ্ঠীর কাছ থেকে মতামত এবং তথ্য সংগ্রহ করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="solicit_feedback"
                      value="yes"
                      checked={formData.solicit_feedback === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, solicit_feedback: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="solicit_feedback"
                      value="no"
                      checked={formData.solicit_feedback === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, solicit_feedback: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="solicit_feedback"
                      value="na"
                      checked={formData.solicit_feedback === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, solicit_feedback: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">5.</td>
              <td className="p-3 border-r">
                <div>Does your Federation regularly interact with target groups?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি নিয়মিতভাবে টার্গেট গোষ্ঠীর সাথে যোগাযোগ করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="regular_interaction"
                      value="yes"
                      checked={formData.regular_interaction === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, regular_interaction: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="regular_interaction"
                      value="no"
                      checked={formData.regular_interaction === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, regular_interaction: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="regular_interaction"
                      value="na"
                      checked={formData.regular_interaction === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, regular_interaction: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">6.</td>
              <td className="p-3 border-r">
                <div>Does your Federation share program results with relevant workers' leaders or associations for planning and troubleshooting?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি প্রোগ্রামের ফলাফল প্রাসঙ্গিক শ্রমিক নেতাদের বা সমিতির সাথে পরিকল্পনা এবং সমস্যা সমাধানের জন্য শেয়ার করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="share_results"
                      value="yes"
                      checked={formData.share_results === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, share_results: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="share_results"
                      value="no"
                      checked={formData.share_results === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, share_results: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="share_results"
                      value="na"
                      checked={formData.share_results === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, share_results: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </QuestionCard>
  );
}