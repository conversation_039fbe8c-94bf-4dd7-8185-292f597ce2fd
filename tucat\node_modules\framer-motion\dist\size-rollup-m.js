import{jsxs as t,jsx as e}from"react/jsx-runtime";import{createContext as n,useContext as r,useMemo as o,use<PERSON><PERSON>back as a,useLayoutEffect as s,useEffect as i,useRef as c,useInsertionEffect as l,forwardRef as u,Fragment as f,createElement as d}from"react";const p={},m=n({}),g=n({strict:!1}),y=n({transformPagePoint:t=>t,isStatic:!1,reducedMotion:"never"}),h=n({});function v(t){return null!==t&&"object"==typeof t&&"function"==typeof t.start}function w(t){return"string"==typeof t||Array.isArray(t)}const b=["initial","animate","whileInView","whileFocus","whileHover","whileTap","whileDrag","exit"];function S(t){return v(t.animate)||b.some(e=>w(t[e]))}function x(t){const{initial:e,animate:n}=function(t,e){if(S(t)){const{initial:e,animate:n}=t;return{initial:!1===e||w(e)?e:void 0,animate:w(n)?n:void 0}}return!1!==t.inherit?e:{}}(t,r(h));return o(()=>({initial:e,animate:n}),[M(e),M(n)])}function M(t){return Array.isArray(t)?t.join(" "):t}const P="undefined"!=typeof window,T={animation:["animate","variants","whileHover","whileTap","exit","whileInView","whileFocus","whileDrag"],exit:["exit"],drag:["drag","dragControls"],focus:["whileFocus"],hover:["whileHover","onHoverStart","onHoverEnd"],tap:["whileTap","onTap","onTapStart","onTapCancel"],pan:["onPan","onPanStart","onPanSessionStart","onPanEnd"],inView:["whileInView","onViewportEnter","onViewportLeave"],layout:["layout","layoutId"]},k={};for(const t in T)k[t]={isEnabled:e=>T[t].some(t=>!!e[t])};const E=Symbol.for("motionComponentSymbol");function O(t){return t&&"object"==typeof t&&Object.prototype.hasOwnProperty.call(t,"current")}function V(t,e,n){return a(r=>{r&&t.onMount&&t.onMount(r),e&&(r?e.mount(r):e.unmount()),n&&("function"==typeof n?n(r):O(n)&&(n.current=r))},[e])}const W=["setup","read","resolveKeyframes","preUpdate","update","preRender","render","postRender"],R={value:null,addProjectionMetrics:null};function C(t,e){let n=!1,r=!0;const o={delta:0,timestamp:0,isProcessing:!1},a=()=>n=!0,s=W.reduce((t,n)=>(t[n]=function(t,e){let n=new Set,r=new Set,o=!1,a=!1;const s=new WeakSet;let i={delta:0,timestamp:0,isProcessing:!1},c=0;function l(e){s.has(e)&&(u.schedule(e),t()),c++,e(i)}const u={schedule:(t,e=!1,a=!1)=>{const i=a&&o?n:r;return e&&s.add(t),i.has(t)||i.add(t),t},cancel:t=>{r.delete(t),s.delete(t)},process:t=>{i=t,o?a=!0:(o=!0,[n,r]=[r,n],n.forEach(l),e&&R.value&&R.value.frameloop[e].push(c),c=0,n.clear(),o=!1,a&&(a=!1,u.process(t)))}};return u}(a,e?n:void 0),t),{}),{setup:i,read:c,resolveKeyframes:l,preUpdate:u,update:f,preRender:d,render:m,postRender:g}=s,y=()=>{const a=p.useManualTiming?o.timestamp:performance.now();n=!1,p.useManualTiming||(o.delta=r?1e3/60:Math.max(Math.min(a-o.timestamp,40),1)),o.timestamp=a,o.isProcessing=!0,i.process(o),c.process(o),l.process(o),u.process(o),f.process(o),d.process(o),m.process(o),g.process(o),o.isProcessing=!1,n&&e&&(r=!1,t(y))};return{schedule:W.reduce((e,a)=>{const i=s[a];return e[a]=(e,a=!1,s=!1)=>(n||(n=!0,r=!0,o.isProcessing||t(y)),i.schedule(e,a,s)),e},{}),cancel:t=>{for(let e=0;e<W.length;e++)s[W[e]].cancel(t)},state:o,steps:s}}const L=(t=>e=>"string"==typeof e&&e.startsWith(t))("--"),A={test:t=>"number"==typeof t,parse:parseFloat,transform:t=>t},j={...A,transform:t=>((t,e,n)=>n>e?e:n<t?t:n)(0,1,t)},B={...A,default:1},I=t=>({test:e=>"string"==typeof e&&e.endsWith(t)&&1===e.split(" ").length,parse:parseFloat,transform:e=>`${e}${t}`}),X=I("deg"),Y=I("%"),$=I("px"),F=(()=>({...Y,parse:t=>Y.parse(t)/100,transform:t=>Y.transform(100*t)}))(),D=["transformPerspective","x","y","z","translateX","translateY","translateZ","scale","scaleX","scaleY","rotate","rotateX","rotateY","rotateZ","skew","skewX","skewY"],H=(()=>new Set(D))(),Z={...A,transform:Math.round},z={borderWidth:$,borderTopWidth:$,borderRightWidth:$,borderBottomWidth:$,borderLeftWidth:$,borderRadius:$,radius:$,borderTopLeftRadius:$,borderTopRightRadius:$,borderBottomRightRadius:$,borderBottomLeftRadius:$,width:$,maxWidth:$,height:$,maxHeight:$,top:$,right:$,bottom:$,left:$,padding:$,paddingTop:$,paddingRight:$,paddingBottom:$,paddingLeft:$,margin:$,marginTop:$,marginRight:$,marginBottom:$,marginLeft:$,backgroundPositionX:$,backgroundPositionY:$,...{rotate:X,rotateX:X,rotateY:X,rotateZ:X,scale:B,scaleX:B,scaleY:B,scaleZ:B,skew:X,skewX:X,skewY:X,distance:$,translateX:$,translateY:$,translateZ:$,x:$,y:$,z:$,perspective:$,transformPerspective:$,opacity:j,originX:F,originY:F,originZ:$},zIndex:Z,fillOpacity:j,strokeOpacity:j,numOctaves:Z},N=(t,e)=>e&&"number"==typeof t?e.transform(t):t,{schedule:U,cancel:q}=C(queueMicrotask,!1),K=t=>Boolean(t&&t.getVelocity),_="data-"+"framerAppearId".replace(/([a-z])([A-Z])/gu,"$1-$2").toLowerCase();const G=n(null),J=n({}),Q=P?s:i;function tt(t,e,n,o,a){const{visualElement:s}=r(h),u=r(g),f=r(G),d=r(y).reducedMotion,p=c(null);o=o||u.renderer,!p.current&&o&&(p.current=o(t,{visualState:e,parent:s,props:n,presenceContext:f,blockInitialAnimation:!!f&&!1===f.initial,reducedMotionConfig:d}));const m=p.current,v=r(J);!m||m.projection||!a||"html"!==m.type&&"svg"!==m.type||function(t,e,n,r){const{layoutId:o,layout:a,drag:s,dragConstraints:i,layoutScroll:c,layoutRoot:l,layoutCrossfade:u}=e;t.projection=new n(t.latestValues,e["data-framer-portal-id"]?void 0:et(t.parent)),t.projection.setOptions({layoutId:o,layout:a,alwaysMeasureLayout:Boolean(s)||i&&O(i),visualElement:t,animationType:"string"==typeof a?a:"both",initialPromotionConfig:r,crossfade:u,layoutScroll:c,layoutRoot:l})}(p.current,n,a,v);const w=c(!1);l(()=>{m&&w.current&&m.update(n,f)});const b=n[_],S=c(Boolean(b)&&!window.MotionHandoffIsComplete?.(b)&&window.MotionHasOptimisedAnimation?.(b));return Q(()=>{m&&(w.current=!0,window.MotionIsMounted=!0,m.updateFeatures(),U.render(m.render),S.current&&m.animationState&&m.animationState.animateChanges())}),i(()=>{m&&(!S.current&&m.animationState&&m.animationState.animateChanges(),S.current&&(queueMicrotask(()=>{window.MotionHandoffMarkAsComplete?.(b)}),S.current=!1))}),m}function et(t){if(t)return!1!==t.options.allowProjection?t.projection:et(t.parent)}function nt({preloadedFeatures:n,createVisualElement:o,useRender:a,useVisualState:s,Component:i}){function c(n,c){let l;const u={...r(y),...n,layoutId:rt(n)},{isStatic:f}=u,d=x(n),p=s(n,f);if(!f&&P){r(g).strict;const t=function(t){const{drag:e,layout:n}=k;if(!e&&!n)return{};const r={...e,...n};return{MeasureLayout:e?.isEnabled(t)||n?.isEnabled(t)?r.MeasureLayout:void 0,ProjectionNode:r.ProjectionNode}}(u);l=t.MeasureLayout,d.visualElement=tt(i,p,u,o,t.ProjectionNode)}return t(h.Provider,{value:d,children:[l&&d.visualElement?e(l,{visualElement:d.visualElement,...u}):null,a(i,n,V(p,d.visualElement,c),p,f,d.visualElement)]})}n&&function(t){for(const e in t)k[e]={...k[e],...t[e]}}(n),c.displayName=`motion.${"string"==typeof i?i:`create(${i.displayName??i.name??""})`}`;const l=u(c);return l[E]=i,l}function rt({layoutId:t}){const e=r(m).id;return e&&void 0!==t?e+"-"+t:t}const ot={};function at(t,{layout:e,layoutId:n}){return H.has(t)||t.startsWith("origin")||(e||void 0!==n)&&(!!ot[t]||"opacity"===t)}const st={x:"translateX",y:"translateY",z:"translateZ",transformPerspective:"perspective"},it=D.length;function ct(t,e,n){const{style:r,vars:o,transformOrigin:a}=t;let s=!1,i=!1;for(const t in e){const n=e[t];if(H.has(t))s=!0;else if(L(t))o[t]=n;else{const e=N(n,z[t]);t.startsWith("origin")?(i=!0,a[t]=e):r[t]=e}}if(e.transform||(s||n?r.transform=function(t,e,n){let r="",o=!0;for(let a=0;a<it;a++){const s=D[a],i=t[s];if(void 0===i)continue;let c=!0;if(c="number"==typeof i?i===(s.startsWith("scale")?1:0):0===parseFloat(i),!c||n){const t=N(i,z[s]);c||(o=!1,r+=`${st[s]||s}(${t}) `),n&&(e[s]=t)}}return r=r.trim(),n?r=n(e,o?"":r):o&&(r="none"),r}(e,t.transform,n):r.transform&&(r.transform="none")),i){const{originX:t="50%",originY:e="50%",originZ:n=0}=a;r.transformOrigin=`${t} ${e} ${n}`}}const lt=()=>({style:{},transform:{},transformOrigin:{},vars:{}});function ut(t,e,n){for(const r in e)K(e[r])||at(r,n)||(t[r]=e[r])}function ft(t,e){const n={};return ut(n,t.style||{},t),Object.assign(n,function({transformTemplate:t},e){return o(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{}};return ct(n,e,t),Object.assign({},n.vars,n.style)},[e])}(t,e)),n}function dt(t,e){const n={},r=ft(t,e);return t.drag&&!1!==t.dragListener&&(n.draggable=!1,r.userSelect=r.WebkitUserSelect=r.WebkitTouchCallout="none",r.touchAction=!0===t.drag?"none":"pan-"+("x"===t.drag?"y":"x")),void 0===t.tabIndex&&(t.onTap||t.onTapStart||t.whileTap)&&(n.tabIndex=0),n.style=r,n}const pt={offset:"stroke-dashoffset",array:"stroke-dasharray"},mt={offset:"strokeDashoffset",array:"strokeDasharray"};function gt(t,{attrX:e,attrY:n,attrScale:r,pathLength:o,pathSpacing:a=1,pathOffset:s=0,...i},c,l,u){if(ct(t,i,l),c)return void(t.style.viewBox&&(t.attrs.viewBox=t.style.viewBox));t.attrs=t.style,t.style={};const{attrs:f,style:d}=t;f.transform&&(d.transform=f.transform,delete f.transform),(d.transform||f.transformOrigin)&&(d.transformOrigin=f.transformOrigin??"50% 50%",delete f.transformOrigin),d.transform&&(d.transformBox=u?.transformBox??"fill-box",delete f.transformBox),void 0!==e&&(f.x=e),void 0!==n&&(f.y=n),void 0!==r&&(f.scale=r),void 0!==o&&function(t,e,n=1,r=0,o=!0){t.pathLength=1;const a=o?pt:mt;t[a.offset]=$.transform(-r);const s=$.transform(e),i=$.transform(n);t[a.array]=`${s} ${i}`}(f,o,a,s,!1)}const yt=()=>({style:{},transform:{},transformOrigin:{},vars:{},attrs:{}});function ht(t,e,n,r){const a=o(()=>{const n={style:{},transform:{},transformOrigin:{},vars:{},attrs:{}};var o;return gt(n,e,"string"==typeof(o=r)&&"svg"===o.toLowerCase(),t.transformTemplate,t.style),{...n.attrs,style:{...n.style}}},[e]);if(t.style){const e={};ut(e,t.style,t),a.style={...e,...a.style}}return a}const vt=new Set(["animate","exit","variants","initial","style","values","variants","transition","transformTemplate","custom","inherit","onBeforeLayoutMeasure","onAnimationStart","onAnimationComplete","onUpdate","onDragStart","onDrag","onDragEnd","onMeasureDragConstraints","onDirectionLock","onDragTransitionEnd","_dragX","_dragY","onHoverStart","onHoverEnd","onViewportEnter","onViewportLeave","globalTapTarget","ignoreStrict","viewport"]);function wt(t){return t.startsWith("while")||t.startsWith("drag")&&"draggable"!==t||t.startsWith("layout")||t.startsWith("onTap")||t.startsWith("onPan")||t.startsWith("onLayout")||vt.has(t)}let bt=t=>!wt(t);try{"function"==typeof(St=require("@emotion/is-prop-valid").default)&&(bt=t=>t.startsWith("on")?!wt(t):St(t))}catch{}var St;const xt=["animate","circle","defs","desc","ellipse","g","image","line","filter","marker","mask","metadata","path","pattern","polygon","polyline","rect","stop","switch","symbol","svg","text","tspan","use","view"];function Mt(t){return"string"==typeof t&&!t.includes("-")&&!!(xt.indexOf(t)>-1||/[A-Z]/u.test(t))}function Pt(t=!1){return(e,n,r,{latestValues:a},s)=>{const i=(Mt(e)?ht:dt)(n,a,s,e),c=function(t,e,n){const r={};for(const o in t)"values"===o&&"object"==typeof t.values||(bt(o)||!0===n&&wt(o)||!e&&!wt(o)||t.draggable&&o.startsWith("onDrag"))&&(r[o]=t[o]);return r}(n,"string"==typeof e,t),l=e!==f?{...c,...i,ref:r}:{},{children:u}=n,p=o(()=>K(u)?u.get():u,[u]);return d(e,{...l,children:p})}}function Tt(t){const e=[{},{}];return t?.values.forEach((t,n)=>{e[0][n]=t.get(),e[1][n]=t.getVelocity()}),e}function kt(t,e,n,r){if("function"==typeof e){const[o,a]=Tt(r);e=e(void 0!==n?n:t.custom,o,a)}if("string"==typeof e&&(e=t.variants&&t.variants[e]),"function"==typeof e){const[o,a]=Tt(r);e=e(void 0!==n?n:t.custom,o,a)}return e}function Et(t){return K(t)?t.get():t}const Ot=t=>(e,n)=>{const o=r(h),a=r(G),s=()=>function({scrapeMotionValuesFromProps:t,createRenderState:e},n,r,o){return{latestValues:Vt(n,r,o,t),renderState:e()}}(t,e,o,a);return n?s():function(t){const e=c(null);return null===e.current&&(e.current=t()),e.current}(s)};function Vt(t,e,n,r){const o={},a=r(t,{});for(const t in a)o[t]=Et(a[t]);let{initial:s,animate:i}=t;const c=S(t),l=function(t){return Boolean(S(t)||t.variants)}(t);e&&l&&!c&&!1!==t.inherit&&(void 0===s&&(s=e.initial),void 0===i&&(i=e.animate));let u=!!n&&!1===n.initial;u=u||!1===s;const f=u?i:s;if(f&&"boolean"!=typeof f&&!v(f)){const e=Array.isArray(f)?f:[f];for(let n=0;n<e.length;n++){const r=kt(t,e[n]);if(r){const{transitionEnd:t,transition:e,...n}=r;for(const t in n){let e=n[t];if(Array.isArray(e)){e=e[u?e.length-1:0]}null!==e&&(o[t]=e)}for(const e in t)o[e]=t[e]}}}return o}function Wt(t,e,n){const{style:r}=t,o={};for(const a in r)(K(r[a])||e.style&&K(e.style[a])||at(a,t)||void 0!==n?.getValue(a)?.liveStyle)&&(o[a]=r[a]);return o}const Rt={useVisualState:Ot({scrapeMotionValuesFromProps:Wt,createRenderState:lt})};const Ct={useVisualState:Ot({scrapeMotionValuesFromProps:function(t,e,n){const r=Wt(t,e,n);for(const n in t)if(K(t[n])||K(e[n])){r[-1!==D.indexOf(n)?"attr"+n.charAt(0).toUpperCase()+n.substring(1):n]=t[n]}return r},createRenderState:yt})};function Lt(t,e){return function(n,{forwardMotionProps:r}={forwardMotionProps:!1}){return nt({...Mt(n)?Ct:Rt,preloadedFeatures:t,useRender:Pt(r),createVisualElement:e,Component:n})}}const At=Lt()("div");export{At as MotionDiv};
