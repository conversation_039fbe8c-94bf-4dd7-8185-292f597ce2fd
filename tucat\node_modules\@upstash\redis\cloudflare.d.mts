import { R as RedisOptions, a as RequesterConfig, b as Redis$1 } from './zmscore-hRk-rDLY.mjs';
export { A as AppendCommand, B as BitCountCommand, f as BitOpCommand, g as BitPosCommand, C as CopyCommand, D as DBSizeCommand, i as DecrByCommand, h as DecrCommand, j as DelCommand, E as EchoCommand, l as EvalCommand, k as EvalROCommand, n as EvalshaCommand, m as EvalshaROCommand, o as ExistsCommand, q as ExpireAtCommand, p as ExpireCommand, F as FlushAllCommand, r as FlushDBCommand, G as GeoAddCommand, s as GeoAddCommandOptions, u as GeoDistCommand, v as GeoHashCommand, t as GeoMember, w as GeoPosCommand, x as GeoSearchCommand, y as GeoSearchStoreCommand, H as GetBitCommand, z as GetCommand, I as <PERSON><PERSON><PERSON><PERSON>ommand, J as GetExCommand, K as GetRangeCommand, L as GetSetCommand, M as HDelCommand, N as HExistsCommand, O as HExpireCommand, S as HGetAllCommand, Q as HGetCommand, T as HIncrByCommand, V as HIncrByFloatCommand, W as HKeysCommand, X as HLenCommand, Y as HMGetCommand, Z as HMSetCommand, _ as HRandFieldCommand, $ as HScanCommand, a0 as HSetCommand, a1 as HSetNXCommand, a2 as HStrLenCommand, a3 as HValsCommand, a5 as IncrByCommand, a6 as IncrByFloatCommand, a4 as IncrCommand, a7 as JsonArrAppendCommand, a8 as JsonArrIndexCommand, a9 as JsonArrInsertCommand, aa as JsonArrLenCommand, ab as JsonArrPopCommand, ac as JsonArrTrimCommand, ad as JsonClearCommand, ae as JsonDelCommand, af as JsonForgetCommand, ag as JsonGetCommand, ai as JsonMGetCommand, ah as JsonMergeCommand, aj as JsonNumIncrByCommand, ak as JsonNumMultByCommand, al as JsonObjKeysCommand, am as JsonObjLenCommand, an as JsonRespCommand, ao as JsonSetCommand, ap as JsonStrAppendCommand, aq as JsonStrLenCommand, ar as JsonToggleCommand, as as JsonTypeCommand, at as KeysCommand, au as LIndexCommand, av as LInsertCommand, aw as LLenCommand, ax as LMoveCommand, ay as LPopCommand, az as LPushCommand, aA as LPushXCommand, aB as LRangeCommand, aC as LRemCommand, aD as LSetCommand, aE as LTrimCommand, aF as MGetCommand, aG as MSetCommand, aH as MSetNXCommand, aK as PExpireAtCommand, aJ as PExpireCommand, aM as PSetEXCommand, aN as PTtlCommand, aI as PersistCommand, aL as PingCommand, P as Pipeline, aO as PublishCommand, aS as RPopCommand, aT as RPushCommand, aU as RPushXCommand, aP as RandomKeyCommand, aQ as RenameCommand, aR as RenameNXCommand, d as Requester, aV as SAddCommand, aY as SCardCommand, b0 as SDiffCommand, b1 as SDiffStoreCommand, b8 as SInterCommand, b9 as SInterStoreCommand, ba as SIsMemberCommand, bc as SMIsMemberCommand, bb as SMembersCommand, bd as SMoveCommand, be as SPopCommand, bf as SRandMemberCommand, bg as SRemCommand, bh as SScanCommand, bj as SUnionCommand, bk as SUnionStoreCommand, aW as ScanCommand, aX as ScanCommandOptions, bt as ScoreMember, aZ as ScriptExistsCommand, a_ as ScriptFlushCommand, a$ as ScriptLoadCommand, b4 as SetBitCommand, b2 as SetCommand, b3 as SetCommandOptions, b5 as SetExCommand, b6 as SetNxCommand, b7 as SetRangeCommand, bi as StrLenCommand, bl as TimeCommand, bm as TouchCommand, bn as TtlCommand, bo as Type, bp as TypeCommand, bq as UnlinkCommand, U as UpstashRequest, c as UpstashResponse, br as XAddCommand, bs as XRangeCommand, bv as ZAddCommand, bu as ZAddCommandOptions, bw as ZCardCommand, bx as ZCountCommand, by as ZDiffStoreCommand, bz as ZIncrByCommand, bA as ZInterStoreCommand, bB as ZInterStoreCommandOptions, bC as ZLexCountCommand, bD as ZMScoreCommand, bE as ZPopMaxCommand, bF as ZPopMinCommand, bG as ZRangeCommand, bH as ZRangeCommandOptions, bI as ZRankCommand, bJ as ZRemCommand, bK as ZRemRangeByLexCommand, bL as ZRemRangeByRankCommand, bM as ZRemRangeByScoreCommand, bN as ZRevRankCommand, bO as ZScanCommand, bP as ZScoreCommand, bQ as ZUnionCommand, bR as ZUnionCommandOptions, bS as ZUnionStoreCommand, bT as ZUnionStoreCommandOptions, e as errors } from './zmscore-hRk-rDLY.mjs';

type Env = {
    UPSTASH_DISABLE_TELEMETRY?: string;
};

/**
 * Connection credentials for upstash redis.
 * Get them from https://console.upstash.com/redis/<uuid>
 */
type RedisConfigCloudflare = {
    /**
     * UPSTASH_REDIS_REST_URL
     */
    url: string | undefined;
    /**
     * UPSTASH_REDIS_REST_TOKEN
     */
    token: string | undefined;
    /**
     * The signal will allow aborting requests on the fly.
     * For more check: https://developer.mozilla.org/en-US/docs/Web/API/AbortSignal
     */
    signal?: AbortSignal;
    keepAlive?: boolean;
    /**
     * When this flag is enabled, any subsequent commands issued by this client are guaranteed to observe the effects of all earlier writes submitted by the same client.
     */
    readYourWrites?: boolean;
} & RedisOptions & RequesterConfig & Env;
/**
 * Serverless redis client for upstash.
 */
declare class Redis extends Redis$1 {
    /**
     * Create a new redis client
     *
     * @example
     * ```typescript
     * const redis = new Redis({
     *  url: "<UPSTASH_REDIS_REST_URL>",
     *  token: "<UPSTASH_REDIS_REST_TOKEN>",
     * });
     * ```
     */
    constructor(config: RedisConfigCloudflare, env?: Env);
    static fromEnv(env?: {
        UPSTASH_REDIS_REST_URL: string;
        UPSTASH_REDIS_REST_TOKEN: string;
        UPSTASH_DISABLE_TELEMETRY?: string;
    }, opts?: Omit<RedisConfigCloudflare, "url" | "token">): Redis;
}

export { Redis, type RedisConfigCloudflare };
