"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>er, Card<PERSON>itle, CardDescription } from "@/components/ui/card";
import { Responsive<PERSON>ontaine<PERSON>, <PERSON><PERSON>hart as Bar<PERSON>hart<PERSON><PERSON>, Bar, XAxis, <PERSON>Axis, CartesianGrid, LabelList, Toolt<PERSON> } from "recharts";
import { Bar<PERSON><PERSON> } from "lucide-react";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

type Props = {
  avgSectionScores: { [section: number]: number };
  minSectionScores?: { [section: number]: number };
  maxSectionScores?: { [section: number]: number };
  sectionList: { id: number; title: string }[];
  sectionMaxScores: { [section: number]: number };
};

type ViewMode = "min" | "avg" | "max";

export default function SectionAverageChart({
  avgSectionScores,
  minSectionScores = {},
  maxSectionScores = {},
  sectionList,
  sectionMaxScores
}: Props) {
  const [viewMode, setViewMode] = useState<ViewMode>("avg");

  // Get the appropriate scores based on view mode
  const getScores = () => {
    switch (viewMode) {
      case "min": return minSectionScores;
      case "max": return maxSectionScores;
      case "avg":
      default: return avgSectionScores;
    }
  };

  const currentScores = getScores();

  // Calculate total score and max possible score
  const totalRawScore = Object.entries(currentScores).reduce((sum, [section, score]) => {
    const maxScore = sectionMaxScores[Number(section)] || 0;
    return sum + ((score / 10) * maxScore); // Convert normalized score back to raw score
  }, 0);

  const totalMaxScore = Object.values(sectionMaxScores).reduce((sum, score) => sum + score, 0);
  const totalPercentage = totalMaxScore > 0 ? (totalRawScore / totalMaxScore) * 100 : 0;

  // Calculate total avg score for display below chart (always using avg scores)
  const totalAvgRawScore = Object.entries(avgSectionScores).reduce((sum, [section, score]) => {
    const maxScore = sectionMaxScores[Number(section)] || 0;
    return sum + ((score / 10) * maxScore); // Convert normalized score back to raw score
  }, 0);
  const totalAvgPercentage = totalMaxScore > 0 ? (totalAvgRawScore / totalMaxScore) * 100 : 0;

  // Prepare data for the chart
  const data = sectionList.map(section => {
    const normalizedScore = currentScores[section.id] || 0; // Score out of 10
    const maxScore = sectionMaxScores[section.id] || 0;
    const rawScore = (normalizedScore / 10) * maxScore; // Convert to raw score

    return {
      name: section.title.split(" ").reduce((acc, word, i) => {
        if (section.id === 8 && i === 2) {
          return `${acc}${word}\n`; // Line break for long titles
        }
        return `${acc}${i > 0 ? " " : ""}${word}`;
      }, ""),
      value: rawScore, // Store raw score as value
      maxScore: maxScore,
      fill: "#3b82f6",
      sectionId: section.id
    };
  });

  // Get title based on view mode
  const getTitle = () => {
    switch (viewMode) {
      case "min": return "Minimum Section Scores";
      case "max": return "Maximum Section Scores";
      case "avg":
      default: return "Average Section Scores";
    }
  };

  // Get description based on view mode
  const getDescription = () => {
    switch (viewMode) {
      case "min": return "Minimum scores across all federations";
      case "max": return "Maximum scores across all federations";
      case "avg":
      default: return "Average scores across all federations";
    }
  };

  return (
    <Card className="w-full bg-white">
      <CardHeader>
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <BarChart className="h-5 w-5 text-primary" />
              {getTitle()}
            </CardTitle>
            <CardDescription>
              <span className="flex flex-col gap-1">
                <span>{getDescription()}</span>
                <span className="text-sm text-muted-foreground">
                  Each section is normalized to a score out of 10 for comparison
                </span>
              </span>
            </CardDescription>
          </div>
          <Tabs defaultValue="avg" value={viewMode} onValueChange={(value) => setViewMode(value as ViewMode)} className="w-full md:w-auto">
            <TabsList className="grid grid-cols-3 w-full md:w-[300px]">
              <TabsTrigger value="min">Min</TabsTrigger>
              <TabsTrigger value="avg">Avg</TabsTrigger>
              <TabsTrigger value="max">Max</TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent>
        <div className="h-[400px] w-full">
          <ResponsiveContainer width="100%" height="100%">
            <BarChartIcon
              data={data}
              margin={{ top: 30, right: 30, left: 20, bottom: 20 }}
              barSize={50}
            >
              <CartesianGrid
                strokeDasharray="3 3"
                horizontal={true}
                vertical={false}
                stroke="#e5e7eb"
              />
              <XAxis
                dataKey="name"
                stroke="#000"
                fontSize={10}
                tickLine={false}
                axisLine={{ stroke: '#000' }}
                angle={-0}
                interval={0}
                height={60}
                textAnchor="middle"
              />
              <YAxis
                domain={[0, 15]}
                stroke="#000"
                fontSize={12}
                tickLine={false}
                axisLine={{ stroke: '#000' }}
                label={{ value: 'Score', angle: -90, position: 'insideLeft', offset: 0 }}
              />
              <Tooltip
                formatter={(value: number, _name: string, props: any) => {
                  const maxScore = props.payload.maxScore;
                  const percentage = (value / maxScore) * 100;
                  const sectionId = props.payload.sectionId;

                  // For Leadership section, show the highest possible score
                  if (sectionId === 3) {
                    return [
                      `${value.toFixed(2)} / ${maxScore} (${percentage.toFixed(1)}%)\nHighest possible score: ${maxScore}`,
                      'Raw Score'
                    ];
                  }

                  return [
                    `${value.toFixed(2)} / ${maxScore} (${percentage.toFixed(1)}%)`,
                    'Raw Score'
                  ];
                }}
              />
              <Bar
                dataKey="value"
                radius={[4, 4, 0, 0]}
              >
                <LabelList
                  dataKey="value"
                  position="top"
                  fill="#000"
                  fontSize={12}
                  formatter={(value: number, payload: any) => {
                    const maxScore = payload && typeof payload.maxScore !== 'undefined' ? payload.maxScore : '';
                    return maxScore ? `${value.toFixed(1)} / ${maxScore}` : value.toFixed(1);
                  }}
                />
              </Bar>
            </BarChartIcon>
          </ResponsiveContainer>
        </div>

        {/* Total average score summary */}
        <div className="mt-6 p-4 bg-muted/20 rounded-md">
          <h4 className="font-medium text-center">
            Total Average Score: {totalAvgRawScore.toFixed(2)} out of 90 ({totalAvgPercentage.toFixed(1)}%)
          </h4>
          <p className="text-sm text-muted-foreground text-center mt-1">
            This represents the average performance across all federations
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
