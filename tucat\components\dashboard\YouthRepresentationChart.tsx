"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>le, CardDescription, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Percent, TrendingUp, TrendingDown, MinusCircle } from "lucide-react";
import { ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, Legend, Cell, ReferenceLine } from "recharts";
import * as React from "react";
import type { Federation } from "@/lib/types";

interface YouthRepresentationChartProps {
  federations: Partial<Federation>[];
}

interface CustomTooltipProps {
  active?: boolean;
  payload?: any[];
  label?: string;
}

const CustomTooltip = ({ active, payload, label }: CustomTooltipProps) => {
  if (active && payload && payload.length) {
    const youthCategory = payload[0].payload.originalValue;
    let status = "";
    let color = "";

    if (youthCategory === "above-75%" || youthCategory === "51-75%") {
      status = "High Youth Engagement";
      color = "text-green-600";
    } else if (youthCategory === "26-50%") {
      status = "Moderate Youth Engagement";
      color = "text-yellow-600";
    } else {
      status = "Low Youth Engagement";
      color = "text-red-600";
    }

    return (
      <div className="bg-background/95 backdrop-blur-sm border border-border p-4 rounded-lg shadow-md min-w-[200px]">
        <p className="font-semibold mb-1">{label}</p>
        <div className="space-y-1">
          <p className="text-sm">
            Youth Percentage: <span className="font-medium">{youthCategory}</span>
          </p>
          <p className={`text-sm font-medium ${color}`}>{status}</p>
        </div>
      </div>
    );
  }
  return null;
};

const getCategoryValue = (category: string): number => {
  switch (category) {
    case "above-75%": return 87.5; // midpoint of 75-100%
    case "51-75%": return 63; // midpoint of 51-75%
    case "26-50%": return 38; // midpoint of 26-50%
    case "0-25%": return 12.5; // midpoint of 0-25%
    default: return 0;
  }
};

export default function YouthRepresentationChart({ federations }: YouthRepresentationChartProps) {
  const chartData = federations
    .map(f => ({
      name: f.id?.toUpperCase() || "UNNAMED FEDERATION",
      youth: getCategoryValue(f.youth_percentage || "0-25%"),
      originalValue: f.youth_percentage || "0-25%"
    }))
    .sort((a, b) => b.youth - a.youth);

  // Calculate statistics
  const highEngagementCount = federations.filter(f => 
    f.youth_percentage === "above-75%" || f.youth_percentage === "51-75%"
  ).length;
  const moderateEngagementCount = federations.filter(f => 
    f.youth_percentage === "26-50%"
  ).length;
  const lowEngagementCount = federations.filter(f => 
    f.youth_percentage === "0-25%" || !f.youth_percentage
  ).length;

  const avgYouth = Math.round(chartData.reduce((sum, item) => sum + item.youth, 0) / chartData.length);

  return (
    <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
      <CardHeader className="pb-2 bg-muted/10">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <Percent className="h-5 w-5 text-primary" />
              Youth Representation
            </CardTitle>
            <CardDescription>
              Analysis of youth membership across federations
            </CardDescription>
          </div>
          <Badge variant="outline" className="bg-primary/5">
            Avg: {avgYouth}%
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="h-[500px] bg-muted/10 p-4 rounded-lg border border-muted/50">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart 
              data={chartData} 
              layout="vertical" 
              margin={{ top: 35, right: 30, left: 160, bottom: 20 }}
            >
              <defs>
                <linearGradient id="highGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="#059669" stopOpacity={0.8} />
                  <stop offset="100%" stopColor="#10b981" stopOpacity={1} />
                </linearGradient>
                <linearGradient id="moderateGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="#d97706" stopOpacity={0.8} />
                  <stop offset="100%" stopColor="#f59e0b" stopOpacity={1} />
                </linearGradient>
                <linearGradient id="lowGradient" x1="0" y1="0" x2="1" y2="0">
                  <stop offset="0%" stopColor="#dc2626" stopOpacity={0.8} />
                  <stop offset="100%" stopColor="#ef4444" stopOpacity={1} />
                </linearGradient>
              </defs>
              <XAxis 
                type="number" 
                domain={[0, 100]} 
                tick={{ fontSize: 12 }}
                tickCount={5}
              />
              <YAxis 
                dataKey="name" 
                type="category" 
                tick={{ fontSize: 11 }} 
                width={150}
              />
              <Tooltip content={<CustomTooltip />} />
              <ReferenceLine 
                x={25} 
                stroke="#ef4444" 
                strokeDasharray="3 3" 
                label={{ 
                  value: 'Low', 
                  position: 'top', 
                  fill: '#ef4444',
                  fontSize: 11,
                  dy: -10
                }} 
              />
              <ReferenceLine 
                x={50} 
                stroke="#f59e0b" 
                strokeDasharray="3 3" 
                label={{ 
                  value: 'Moderate', 
                  position: 'top', 
                  fill: '#f59e0b',
                  fontSize: 11,
                  dy: -10
                }} 
              />
              <ReferenceLine 
                x={75} 
                stroke="#10b981" 
                strokeDasharray="3 3" 
                label={{ 
                  value: 'High', 
                  position: 'top', 
                  fill: '#10b981',
                  fontSize: 11,
                  dy: -10
                }} 
              />
              <Bar 
                dataKey="youth" 
                name="Youth %" 
                radius={[0, 4, 4, 0]}
                maxBarSize={16}
              >
                {chartData.map((entry, idx) => (
                  <Cell 
                    key={`cell-${idx}`} 
                    fill={
                      entry.youth >= 75 ? 'url(#highGradient)' :
                      entry.youth >= 50 ? 'url(#highGradient)' :
                      entry.youth >= 25 ? 'url(#moderateGradient)' :
                      'url(#lowGradient)'
                    } 
                  />
                ))}
              </Bar>
            </BarChart>
          </ResponsiveContainer>
        </div>
        <div className="mt-6 grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="bg-muted/10 p-4 rounded-lg border border-muted/50">
            <div className="flex items-center gap-2 mb-1">
              <TrendingUp className="h-4 w-4 text-green-500" />
              <span className="font-medium text-sm">High Engagement</span>
            </div>
            <p className="text-2xl font-bold text-green-600">{highEngagementCount}</p>
            <p className="text-xs text-muted-foreground mt-1">Federations with ≥50% youth</p>
          </div>
          <div className="bg-muted/10 p-4 rounded-lg border border-muted/50">
            <div className="flex items-center gap-2 mb-1">
              <MinusCircle className="h-4 w-4 text-yellow-500" />
              <span className="font-medium text-sm">Moderate Engagement</span>
            </div>
            <p className="text-2xl font-bold text-yellow-600">{moderateEngagementCount}</p>
            <p className="text-xs text-muted-foreground mt-1">Federations with 26-50% youth</p>
          </div>
          <div className="bg-muted/10 p-4 rounded-lg border border-muted/50">
            <div className="flex items-center gap-2 mb-1">
              <TrendingDown className="h-4 w-4 text-red-500" />
              <span className="font-medium text-sm">Low Engagement</span>
            </div>
            <p className="text-2xl font-bold text-red-600">{lowEngagementCount}</p>
            <p className="text-xs text-muted-foreground mt-1">Federations with ≤25% youth</p>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
