"use client";

import { createContext, useContext, ReactNode } from 'react';
import { useAssessment, AssessmentState } from '@/lib/hooks/useAssessment';

type DashboardContextType = AssessmentState & {
  loadAssessment: (federationId?: string) => Promise<void>;
};

const DashboardContext = createContext<DashboardContextType | undefined>(undefined);

export function DashboardProvider({ children }: { children: ReactNode }) {
  const { loadAssessment, ...assessmentState } = useAssessment();

  const value = {
    ...assessmentState,
    loadAssessment,
  };

  return (
    <DashboardContext.Provider value={value}>
      {children}
    </DashboardContext.Provider>
  );
}

export function useDashboardContext() {
  const context = useContext(DashboardContext);
  if (context === undefined) {
    throw new Error('useDashboardContext must be used within a DashboardProvider');
  }
  return context;
}
