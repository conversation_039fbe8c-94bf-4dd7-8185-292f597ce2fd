import { AssessmentLayout } from "@/components/AssessmentLayout";
import { AssessmentProvider } from "@/components/AssessmentContext";
import { SectionI } from "@/components/sections/SectionI";
import { SectionII } from "@/components/sections/SectionII";
import { SectionIII } from "@/components/sections/SectionIII";
import { SectionIV } from "@/components/sections/SectionIV";
import { SectionV } from "@/components/sections/SectionV";
import { SectionVI } from "@/components/sections/SectionVI";
import { SectionVII } from "@/components/sections/SectionVII";
import { SectionVIII } from "@/components/sections/SectionVIII";
import { SectionIX } from "@/components/sections/SectionIX";
import { SectionX } from "@/components/sections/SectionX";
import { SectionXI } from "@/components/sections/SectionXI";

export default function AssessmentPage() {
  return (
    <AssessmentProvider>
      <AssessmentLayout>
        <SectionI />
        <SectionII />
        <SectionIII />
        <SectionIV />
        <SectionV />
        <SectionVI />
        <SectionVII />
        <SectionVIII />
        <SectionIX />
        <SectionX />
        <SectionXI />
      </AssessmentLayout>
    </AssessmentProvider>
  );
}