"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { useDashboardContext } from "./DashboardProvider";
import { Users } from "lucide-react";

export function YouthRepresentationCard() {
  const { assessments, isLoading } = useDashboardContext();

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Youth Representation</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="h-[300px] flex items-center justify-center">
            Loading...
          </div>
        </CardContent>
      </Card>
    );
  }

  // Find the assessment for Section VIII (assuming Youth Representation is part of this section)
  const sectionEightAssessment = assessments.find(a => a.section_number === 8);

  // Placeholder for youth representation data
  const youthData = {
    "Youth in Leadership": sectionEightAssessment?.representation_effectiveness || "N/A",
    "Youth Engagement Programs": sectionEightAssessment?.member_involvement || "N/A",
    // Add other relevant youth representation metrics from Section VIII or other sections
  };

  return (
    <Card>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-base font-medium">Youth Representation</CardTitle>
        <Users className="h-4 w-4 text-muted-foreground" />
      </CardHeader>
      <CardContent>
        <div className="text-2xl font-bold">Coming Soon</div>
        <p className="text-xs text-muted-foreground">
          Detailed youth representation metrics will be displayed here.
        </p>
        {/* Display youth representation data */}
        {/* <div>
          <p><strong>Youth in Leadership:</strong> {youthData["Youth in Leadership"]}</p>
          <p><strong>Youth Engagement Programs:</strong> {youthData["Youth Engagement Programs"]}</p>
        </div> */}
      </CardContent>
    </Card>
  );
}