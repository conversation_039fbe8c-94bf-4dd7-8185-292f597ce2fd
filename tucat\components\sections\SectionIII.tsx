"use client";

import { Card } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";
import { Button } from "@/components/ui/button";
import { useState, useEffect } from "react";

export function SectionIII() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    vision_mission_status: "",
    vision_posted: "",
    vision_in_documents: "",
    vision_for_planning: "",
    decision_making: "",
    emerging_issues_handling: "",
    leadership_development_plan: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 3
    const sectionData = assessments.find(a => a.section_number === 3);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        vision_mission_status: sectionData.vision_mission_status || "",
        vision_posted: sectionData.vision_posted || "",
        vision_in_documents: sectionData.vision_in_documents || "",
        vision_for_planning: sectionData.vision_for_planning || "",
        decision_making: sectionData.decision_making || "",
        emerging_issues_handling: sectionData.emerging_issues_handling || "",
        leadership_development_plan: sectionData.leadership_development_plan || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(3, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  return (
    <div className="space-y-8">
      <Card className="p-6">
        {!federation.id && (
          <Alert variant="destructive" className="mb-6">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              Please complete Section I (Federation Information) before proceeding with this section.
            </AlertDescription>
          </Alert>
        )}

        <h2 className="text-2xl font-bold text-center mb-2">
          LEADERSHIP AND GOVERNANCE
        </h2>
        <h3 className="text-xl text-center mb-4">নেতৃত্ব এবং শাসন</h3>

        <div className="space-y-4 mb-8">
          <div className="space-y-2">
            <p className="font-medium">Objective:</p>
            <p className="text-sm text-muted-foreground">
              To assess the Federation's motivation and stability by reviewing its guiding principles, structure and oversight.
            </p>
            <p className="text-sm text-muted-foreground">
              উদ্দেশ্য: ফেডারেশনের অনুপ্রেরণা এবং স্থিতিশীলতা মূল্যায়ন করা, এর নীতিমালা, কাঠামো এবং নজরদারি পর্যালোচনা করার মাধ্যমে।
            </p>
          </div>

          <div className="space-y-2">
            <p className="font-medium">Vision/Mission:</p>
            <p className="text-sm text-muted-foreground">
              To review the Federation's vision and/or mission statements, learn what drives the Federation, how the statements reflect what it does and how they are communicated and understood by the members of the Federation.
            </p>
            <p className="text-sm text-muted-foreground">
              ভিশন/মিশন: ফেডারেশনের ভিশন এবং/অথবা মিশন বিবৃতি পর্যালোচনা করা, কীভাবে ফেডারেশন পরিচালিত হয়, ভিশন/মিশন কীভাবে এর কাজকে প্রতিফলিত করে এবং কীভাবে ফেডারেশনের সদস্যদের মধ্যে ভিশন/মিশন জানানো ও শেখানো হয়।
            </p>
          </div>
        </div>

        <div className="space-y-8">
          {/* Vision/Mission Status */}
          <div className="space-y-4">
            <div className="border rounded-lg overflow-hidden">
              <table className="w-full">
                <thead className="bg-muted">
                  <tr>
                    <th colSpan={4} className="px-4 py-2 text-left border-b">
                      <div className="font-semibold">Vision/Mission:</div>
                      <div className="text-sm font-normal">ভিশন/মিশন:</div>
                      <div className="text-sm font-normal mt-2">RESPONDER: ANONYMOUS MEMBERS OF THE FEDERATION</div>
                      <div className="text-sm font-normal">প্রতিক্রিয়াকারী: ফেডারেশনের ছদ্মনামা সদস্য</div>
                    </th>
                  </tr>
                </thead>
                <tbody>
                  <tr className="border-b">
                    <td className="p-4 align-top border-r w-1/4">
                      <RadioGroup
                        value={formData.vision_mission_status}
                        onValueChange={(value) =>
                          setFormData((prev) => ({
                            ...prev,
                            vision_mission_status: value,
                          }))
                        }
                      >
                        <div className="flex items-start mb-2">
                          <RadioGroupItem value="1" id="vision-1" className="mt-1" />
                          <Label htmlFor="vision-1" className="ml-2">
                            <div className="font-medium">The vision and/or mission is</div>
                            <div className="text-sm mt-1">
                              • Not a clearly stated description of what the Federation aspires to achieve or become.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ফেডারেশন কী অর্জন করতে চায় বা কী হতে চায় তার একটি স্পষ্টভাবে উল্লিখিত বিবরণ নাই।
                            </div>
                          </Label>
                        </div>
                      </RadioGroup>
                    </td>
                    <td className="p-4 align-top border-r w-1/4">
                      <RadioGroup
                        value={formData.vision_mission_status}
                        onValueChange={(value) =>
                          setFormData((prev) => ({
                            ...prev,
                            vision_mission_status: value,
                          }))
                        }
                      >
                        <div className="flex items-start mb-2">
                          <RadioGroupItem value="2" id="vision-2" className="mt-1" />
                          <Label htmlFor="vision-2" className="ml-2">
                            <div className="font-medium">The vision and/or mission is</div>
                            <div className="text-sm mt-1">
                              • A moderately clear or specific understanding of what the Federation aspires to become or achieve.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ফেডারেশন কী হতে চায় বা কী অর্জন করতে চায় তার একটি মাঝারি স্পষ্ট বা নির্দিষ্ট ধারণা আছে।
                            </div>
                            <div className="text-sm mt-1">
                              • Not widely held by members, and Rarely used to direct actions.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ব্যাপকভাবে সদস্যদের জানা নয়, এবং খুব কমই কর্ম পরিকল্পনা করতে ব্যবহৃত হয়।
                            </div>
                          </Label>
                        </div>
                      </RadioGroup>
                    </td>
                    <td className="p-4 align-top border-r w-1/4">
                      <RadioGroup
                        value={formData.vision_mission_status}
                        onValueChange={(value) =>
                          setFormData((prev) => ({
                            ...prev,
                            vision_mission_status: value,
                          }))
                        }
                      >
                        <div className="flex items-start mb-2">
                          <RadioGroupItem value="3" id="vision-3" className="mt-1" />
                          <Label htmlFor="vision-3" className="ml-2">
                            <div className="font-medium">The vision and/or mission is</div>
                            <div className="text-sm mt-1">
                              • A clear, specific statement of what the Federation aspires to become or achieve.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              একটি স্পষ্ট ও নির্দিষ্ট বিবৃতি আছে।
                            </div>
                            <div className="text-sm mt-1">
                              • Well-known to most but not all members.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              অধিকাংশ সদস্যদের কাছে পরিচিত, কিন্তু সব সদস্যদের কাছে নয়।
                            </div>
                            <div className="text-sm mt-1">
                              • Sometimes used to direct actions and to set priorities.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              কখনও কখনও কর্ম পরিকল্পনা করতে এবং অগ্রাধিকার নির্ধারণ করতে ব্যবহৃত হয়।
                            </div>
                          </Label>
                        </div>
                      </RadioGroup>
                    </td>
                    <td className="p-4 align-top w-1/4">
                      <RadioGroup
                        value={formData.vision_mission_status}
                        onValueChange={(value) =>
                          setFormData((prev) => ({
                            ...prev,
                            vision_mission_status: value,
                          }))
                        }
                      >
                        <div className="flex items-start mb-2">
                          <RadioGroupItem value="4" id="vision-4" className="mt-1" />
                          <Label htmlFor="vision-4" className="ml-2">
                            <div className="font-medium">The vision and/or mission is</div>
                            <div className="text-sm mt-1">
                              • A clear, specific and forceful understanding of what the Federation aspires to become or to achieve.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ফেডারেশন কী হতে চায় বা কী অর্জন করতে চায় তার একটি স্পষ্ট, নির্দিষ্ট এবং শক্তিশালী ধারণা আছে।
                            </div>
                            <div className="text-sm mt-1">
                              • Well-communicated and broadly held within the Federation.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              ফেডারেশনের মধ্যে ভালোভাবে ভিশন/মিশন নিয়ে যোগাযোগ করা হয় এবং ব্যাপকভাবে গ্রহণযোগ্য।
                            </div>
                             <div className="text-sm mt-1">
                              • Consistently used to direct actions and to set priorities.
                            </div>
                            <div className="text-sm text-muted-foreground mt-1">
                              নিয়মিতভাবে কর্ম পরিকল্পনা করতে এবং অগ্রাধিকার নির্ধারণ করতে ভিশন/মিশন ব্যবহৃত হয়।
                            </div>
                          </Label>
                        </div>
                      </RadioGroup>
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>

          {/* Vision/Mission Checklist */}
          <div className="border rounded-lg overflow-hidden bg-blue-50/50">
            <div className="p-4 border-b">
              <div className="font-semibold">Vision/Mission Checklist</div>
              <div className="text-sm">ভিশন/মিশন: হ্যাঁ/না চেকলিস্ট</div>
            </div>
            <table className="w-full border-collapse">
              <thead className="bg-muted/50">
                <tr className="border-b">
                  <th className="p-3 text-left">#</th>
                  <th className="p-3 text-left">Question</th>
                  <th className="p-3 text-center w-24">Yes</th>
                  <th className="p-3 text-center w-24">No</th>
                  <th className="p-3 text-center w-24">N/A</th>
                </tr>
              </thead>
              <tbody>
              <tr className="border-b">
                <td className="p-3 w-12 align-top">1.</td>
                <td className="p-3 border-r">
                  <div>Is the vision or mission statement posted where members see it regularly?</div>
                  <div className="text-sm text-muted-foreground">
                    ভিশন বা মিশন বিবৃতি কি এমন জায়গায় টাঙানো রয়েছে যেখানে সদস্যরা নিয়মিতভাবে দেখতে পারেন?
                  </div>
                </td>
                <td colSpan={3} className="p-3">
                  <div className="flex justify-center gap-12">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_posted"
                        value="yes"
                        checked={formData.vision_posted === "yes"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_posted: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>Yes</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_posted"
                        value="no"
                        checked={formData.vision_posted === "no"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_posted: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>No</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_posted"
                        value="na"
                        checked={formData.vision_posted === "na"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_posted: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>N/A</span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr className="border-b">
                <td className="p-3 w-12 align-top">2.</td>
                <td className="p-3 border-r">
                  <div>Is the statement(s) used in any document?</div>
                  <div className="text-sm text-muted-foreground">
                    এই বিবৃতিগুলি কি কোনো নথিতে ব্যবহৃত হয়?
                  </div>
                </td>
                <td colSpan={3} className="p-3">
                  <div className="flex justify-center gap-12">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_in_documents"
                        value="yes"
                        checked={formData.vision_in_documents === "yes"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_in_documents: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>Yes</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_in_documents"
                        value="no"
                        checked={formData.vision_in_documents === "no"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_in_documents: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>No</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_in_documents"
                        value="na"
                        checked={formData.vision_in_documents === "na"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_in_documents: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>N/A</span>
                    </label>
                  </div>
                </td>
              </tr>
              <tr>
                <td className="p-3 w-12 align-top">3.</td>
                <td className="p-3 border-r">
                  <div>Are the vision and mission used to set priorities or for developing Annual Plan?</div>
                  <div className="text-sm text-muted-foreground">
                    ভিশন এবং মিশন কি ফেডারেশনের অগ্রাধিকার নির্ধারণ করতে বা বার্ষিক পরিকল্পনা তৈরির জন্য ব্যবহৃত হয়?
                  </div>
                </td>
                <td colSpan={3} className="p-3">
                  <div className="flex justify-center gap-12">
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_for_planning"
                        value="yes"
                        checked={formData.vision_for_planning === "yes"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_for_planning: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>Yes</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_for_planning"
                        value="no"
                        checked={formData.vision_for_planning === "no"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_for_planning: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>No</span>
                    </label>
                    <label className="flex items-center gap-2 cursor-pointer">
                      <input
                        type="radio"
                        name="vision_for_planning"
                        value="na"
                        checked={formData.vision_for_planning === "na"}
                        onChange={(e) =>
                          setFormData((prev) => ({ ...prev, vision_for_planning: e.target.value }))
                        }
                        className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                      />
                      <span>N/A</span>
                    </label>
                  </div>
                </td>
              </tr>
              </tbody>
            </table>
          </div>

          {/* Decision Making Process */}
          <div className="space-y-4">
            <Label className="text-base">
              4. How inclusive is the decision-making process within the federation?
              <span className="block text-sm text-muted-foreground">
                ফেডারেশনের সিদ্ধান্ত গ্রহণের প্রক্রিয়া কতটা অন্তর্ভুক্তিমূলক?
              </span>
            </Label>
            <RadioGroup
              className="space-y-2"
              value={formData.decision_making}
              onValueChange={(value) =>
                setFormData((prev) => ({ ...prev, decision_making: value }))
              }
            >
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="a" id="decision-a" className="mt-1" />
                <Label htmlFor="decision-a" className="leading-tight">
                  Highly inclusive – Decisions are made with input from all members.
                  <span className="block text-sm text-muted-foreground">
                    অত্যন্ত অন্তর্ভুক্তিমূলক। সকল সদস্য ও প্রান্তিক গ্রুপগুলির মতামত নিয়ে সিদ্ধান্ত নেওয়া হয়।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="b" id="decision-b" className="mt-1" />
                <Label htmlFor="decision-b" className="leading-tight">
                  Moderately inclusive – Some members are consulted, but not all voices heard equally.
                  <span className="block text-sm text-muted-foreground">
                    মাঝারি অন্তর্ভুক্তিমূলক। কিছু সদস্যের সঙ্গে পরামর্শ করা হয়, তবে সব সদস্যের মতামত সমানভাবে শোনা হয় না।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="c" id="decision-c" className="mt-1" />
                <Label htmlFor="decision-c" className="leading-tight">
                  Somewhat inclusive – Decision-making is limited to a few key individuals.
                  <span className="block text-sm text-muted-foreground">
                    কিছুটা অন্তর্ভুক্তিমূলক। সিদ্ধান্ত – গ্রহণ কেবলমাত্র কয়েকজন গুরুত্বপূর্ণ ব্যক্তির মধ্যে সীমাবদ।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="d" id="decision-d" className="mt-1" />
                <Label htmlFor="decision-d" className="leading-tight">
                  Not inclusive – Decisions are made without input from most members.
                  <span className="block text-sm text-muted-foreground">
                    অন্তর্ভুক্তিমূলক নয়। বেশিরভাগ সদস্যের মতামত ছাড়াই সিদ্ধান্ত নেওয়া হয়।
                  </span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Emerging Issues */}
          <div className="space-y-4">
            <Label className="text-base">
              5. How effective is the leadership in addressing emerging issues (such as gender, youth, and industrial changes)?
              <span className="block text-sm text-muted-foreground">
                নতুন উদীয়মান বিষয়গুলো (যেমন: লিঙ্গ, যুবক এবং শিল্পের পরিবর্তন) সমাধানে নেতৃত্ব কতটা কার্যকর?
              </span>
            </Label>
            <RadioGroup
              className="space-y-2"
              value={formData.emerging_issues_handling}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  emerging_issues_handling: value,
                }))
              }
            >
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="a" id="emerging-a" className="mt-1" />
                <Label htmlFor="emerging-a" className="leading-tight">
                  Very effective – Leadership proactively addresses emerging issues.
                  <span className="block text-sm text-muted-foreground">
                    খুব কার্যকর – খুব সক্রিয়ভাবে উদীয়মান বিষয়গুলো সমাধান করে।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="b" id="emerging-b" className="mt-1" />
                <Label htmlFor="emerging-b" className="leading-tight">
                  Moderately effective – Leadership addresses some issues, but not consistently.
                  <span className="block text-sm text-muted-foreground">
                    মাঝারি কার্যকর নেতৃত্ব কিছু বিষয় সমাধান করে, তবে ধারাবাহিকভাবে নয়।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="c" id="emerging-c" className="mt-1" />
                <Label htmlFor="emerging-c" className="leading-tight">
                  Somewhat effective – Leadership occasionally addresses emerging issues but not adequately.
                  <span className="block text-sm text-muted-foreground">
                    কিছুটা কার্যকর – মাঝে মাঝে উদীয়মান বিষয়গুলো সমাধান করে কিন্তু পর্যাপ্ত নয়।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="d" id="emerging-d" className="mt-1" />
                <Label htmlFor="emerging-d" className="leading-tight">
                  Not effective – Leadership does not address emerging issues.
                  <span className="block text-sm text-muted-foreground">
                    কার্যকর নয় – নেতৃত্ব উদীয়মান বিষয়গুলো সমাধান করে না।
                  </span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          {/* Leadership Development Plan */}
          <div className="space-y-4">
            <Label className="text-base">
              6. Does the federation have a formal leadership development plan?
              <span className="block text-sm text-muted-foreground">
                ফেডারেশনের কি একটি আনুষ্ঠানিক নেতৃত্ব বিকাশ করার জন্য পরিকল্পনা রয়েছে?
              </span>
            </Label>
            <RadioGroup
              className="space-y-2"
              value={formData.leadership_development_plan}
              onValueChange={(value) =>
                setFormData((prev) => ({
                  ...prev,
                  leadership_development_plan: value,
                }))
              }
            >
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="a" id="plan-a" className="mt-1" />
                <Label htmlFor="plan-a" className="leading-tight">
                  Yes, a formal plan is in place and regularly updated.
                  <span className="block text-sm text-muted-foreground">
                    হ্যাঁ, একটি আনুষ্ঠানিক পরিকল্পনা রয়েছে এবং নিয়মিত আপডেট করা হয়।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="b" id="plan-b" className="mt-1" />
                <Label htmlFor="plan-b" className="leading-tight">
                  Somewhat – There is a plan but it lacks consistency in implementation.
                  <span className="block text-sm text-muted-foreground">
                    কিছু একটি পরিকল্পনা আছে, তবে এটি বাস্তবায়নে ধারাবাহিকতার অভাব রয়েছে।
                  </span>
                </Label>
              </div>
              <div className="flex items-start space-x-2">
                <RadioGroupItem value="c" id="plan-c" className="mt-1" />
                <Label htmlFor="plan-c" className="leading-tight">
                  No, there is no formal leadership development plan.
                  <span className="block text-sm text-muted-foreground">
                    না, কোনো আনুষ্ঠানিক নেতৃত্ব বিকাশ পরিকল্পনা নাই।
                  </span>
                </Label>
              </div>
            </RadioGroup>
          </div>

          <div className="pt-6 space-x-4 flex justify-end">
            <Button
              onClick={handleSave}
              disabled={isLoading || !federation.id}
            >
              {isLoading ? "Saving..." : "Save Section"}
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}