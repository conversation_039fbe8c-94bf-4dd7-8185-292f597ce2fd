"use client";

import { useDashboardContext } from "@/components/dashboard/DashboardProvider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Building2, Calendar, Users, UserCircle, Percent } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";

export function FederationInfoCard() {
  const { federation, isLoading } = useDashboardContext();

  if (isLoading) {
    return (
      <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full">
        <CardHeader className="pb-2">
          <Skeleton className="h-6 w-[180px] mb-2" />
          <Skeleton className="h-4 w-[250px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="flex items-start space-x-3">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-[100px]" />
                  <Skeleton className="h-3 w-full" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
      <CardHeader className="pb-2 bg-muted/10">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              Federation Profile
            </CardTitle>
            <CardDescription>Key details about your federation</CardDescription>
          </div>
          <Badge variant="outline" className="text-xs bg-primary/5 hover:bg-primary/10">
            {federation.federation_type || "Trade Union"}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-6">
          <div className="flex items-start space-x-4">
            <div className="bg-primary/10 p-2.5 rounded-full">
              <Building2 className="h-5 w-5 text-primary" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm text-muted-foreground">Federation Name</h3>
              <p className="font-semibold">{federation.name || "Not specified"}</p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="bg-amber-500/10 p-2.5 rounded-full">
              <Calendar className="h-5 w-5 text-amber-500" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm text-muted-foreground">Established</h3>
              <p className="font-semibold">
                {federation.establishment_year ? (
                  <>
                    {federation.establishment_year}
                    <span className="text-sm font-normal text-muted-foreground ml-2">
                      ({new Date().getFullYear() - federation.establishment_year} years ago)
                    </span>
                  </>
                ) : (
                  "Not specified"
                )}
              </p>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="bg-blue-500/10 p-2.5 rounded-full">
              <UserCircle className="h-5 w-5 text-blue-500" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm text-muted-foreground">Leadership</h3>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-1">
                <div className="bg-muted/30 p-2 rounded-md">
                  <p className="text-xs text-muted-foreground">President</p>
                  <p className="font-medium">
                    {federation.president_name || "Not specified"}
                    {federation.president_age ? (
                      <span className="text-xs text-muted-foreground ml-1">
                        ({federation.president_age} years)
                      </span>
                    ) : ""}
                  </p>
                </div>
                <div className="bg-muted/30 p-2 rounded-md">
                  <p className="text-xs text-muted-foreground">Secretary</p>
                  <p className="font-medium">
                    {federation.secretary_name || "Not specified"}
                    {federation.secretary_age ? (
                      <span className="text-xs text-muted-foreground ml-1">
                        ({federation.secretary_age} years)
                      </span>
                    ) : ""}
                  </p>
                </div>
              </div>
            </div>
          </div>

          <div className="flex items-start space-x-4">
            <div className="bg-green-500/10 p-2.5 rounded-full">
              <Percent className="h-5 w-5 text-green-500" />
            </div>
            <div className="flex-1">
              <h3 className="font-medium text-sm text-muted-foreground">Youth Representation</h3>
              <div className="mt-1">
                <Badge variant="secondary" className="font-semibold">
                  {federation.youth_percentage || "Not specified"}
                </Badge>
              </div>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
