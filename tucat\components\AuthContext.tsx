"use client";

import { createContext, useContext, ReactNode, useState, useEffect } from 'react';
import { User, UserRole, AuthState } from '@/lib/types';
import { redis, safeRedisOperation } from '@/lib/upstash';
import { useRouter } from 'next/navigation';
import { toast } from 'sonner';

interface AuthContextType extends AuthState {
  login: (email: string, password: string) => Promise<void>;
  register: (username: string, email: string, password: string, federation_id: string, role: UserRole) => Promise<void>;
  logout: () => void;
  checkAuth: () => Promise<boolean>;
  getCurrentFederationId: () => string | null;
  updateUserFederation: (federationId: string) => Promise<boolean>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const [authState, setAuthState] = useState<AuthState>({
    user: null,
    isAuthenticated: false,
    isLoading: true,
    error: null,
  });

  // Check if user is authenticated on initial load
  useEffect(() => {
    const checkUserAuth = async () => {
      try {
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          let user: User;
          try {
            user = JSON.parse(storedUser) as User;
          } catch (parseError) {
            console.error('Error parsing user data from localStorage:', parseError);
            localStorage.removeItem('user'); // Remove invalid data
            throw new Error('Invalid user data format');
          }

          if (!user || !user.id) {
            localStorage.removeItem('user'); // Remove invalid data
            throw new Error('Invalid user data structure');
          }

          // Verify user exists in database
          const dbUser = await safeRedisOperation(() => redis.get(`user:${user.id}`));
          if (dbUser) {
            // Parse the database user to ensure we have the latest data
            let parsedDbUser: User;
            try {
              // Check if dbUser is already an object or needs to be parsed
              if (typeof dbUser === 'object' && !Array.isArray(dbUser)) {
                parsedDbUser = dbUser as User;
              } else if (typeof dbUser === 'string') {
                parsedDbUser = JSON.parse(dbUser) as User;
              } else {
                throw new Error('Invalid database user data format');
              }
            } catch (parseError) {
              console.error('Error parsing user data from database:', parseError);
              throw new Error('Invalid database user data format');
            }

            setAuthState({
              user,
              isAuthenticated: true,
              isLoading: false,
              error: null,
            });
            return;
          }
        }
        // No valid user found
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: null,
        });
      } catch (error) {
        console.error('Auth check error:', error);
        localStorage.removeItem('user'); // Clean up potentially corrupted data
        setAuthState({
          user: null,
          isAuthenticated: false,
          isLoading: false,
          error: error instanceof Error ? error.message : 'Authentication check failed',
        });
      }
    };

    checkUserAuth();
  }, []);

  const login = async (email: string, password: string) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      // In a real app, we would hash the password before comparing
      const users = await safeRedisOperation(() => redis.get('users'));

      let userList = [];
      try {
        // Check if users is already an object or needs to be parsed
        if (users && typeof users === 'string') {
          userList = JSON.parse(users);
        } else if (users && typeof users === 'object') {
          // Ensure userList is an array
          userList = Array.isArray(users) ? users : [];
        }

        // Final check to ensure userList is always an array
        if (!Array.isArray(userList)) {
          console.error('userList is not an array:', userList);
          userList = [];
        }
      } catch (parseError) {
        console.error('Error parsing users data:', parseError);
        throw new Error('Error retrieving user data');
      }

      const user = userList.find((u: User) => u.email === email);

      if (!user || user.password !== password) {
        throw new Error('Invalid email or password');
      }

      // Remove password from user object before storing in state
      const { password: _, ...userWithoutPassword } = user;

      try {
        localStorage.setItem('user', JSON.stringify(userWithoutPassword));
        // Also set a cookie for server-side auth checks
        document.cookie = `user=${JSON.stringify(userWithoutPassword)}; path=/; max-age=86400`;
      } catch (storageError) {
        console.error('Error storing user data in localStorage:', storageError);
        // Continue with login even if localStorage fails
      }

      setAuthState({
        user: userWithoutPassword as User,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });

      toast.success('Logged in successfully');

      // Ensure state is updated before navigation
      await new Promise(resolve => {
        setAuthState(prev => {
          resolve(null);
          return {
            user: userWithoutPassword as User,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          };
        });
      });

      // Set a cookie for server-side auth checks
      document.cookie = `user=${JSON.stringify(userWithoutPassword)}; path=/; max-age=86400; SameSite=Strict`;

      // Use window.location.href for more reliable navigation after login
      window.location.href = '/assessment';
    } catch (error: any) {
      console.error('Login error:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Login failed',
      }));
      toast.error(error.message || 'Login failed');
    }
  };

  const register = async (username: string, email: string, password: string, federation_id: string, role: UserRole) => {
    setAuthState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      // Check if email already exists
      const users = await safeRedisOperation(() => redis.get('users'));

      let userList = [];
      try {
        // Check if users is already an object or needs to be parsed
        if (users && typeof users === 'string') {
          userList = JSON.parse(users);
        } else if (users && typeof users === 'object') {
          // Ensure userList is an array
          userList = Array.isArray(users) ? users : [];
        }

        // Final check to ensure userList is always an array
        if (!Array.isArray(userList)) {
          console.error('userList is not an array:', userList);
          userList = [];
        }
      } catch (parseError) {
        console.error('Error parsing users data:', parseError);
        throw new Error('Error retrieving user data');
      }

      if (userList.some((u: User) => u.email === email)) {
        throw new Error('Email already in use');
      }

      // Validate role - only allow FEDERATION_MEMBER for regular registration
      // In a real app, ADMIN and FEDERATION_ADMIN roles would be assigned through a separate process
      if (role !== UserRole.FEDERATION_MEMBER && role !== UserRole.GUEST) {
        throw new Error('Invalid role selection');
      }

      // Federation selection is now optional during registration
      // Users will select their federation in Section I

      // Create new user
      const newUser: User = {
        id: `user_${Date.now()}_${Math.random().toString(36).slice(2)}`,
        username,
        email,
        password, // In a real app, we would hash the password
        federation_id,
        role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      };

      // Add user to list and save
      userList.push(newUser);
      await safeRedisOperation(() => redis.set('users', JSON.stringify(userList)));

      // Also store individual user record for faster lookups
      await safeRedisOperation(() => redis.set(`user:${newUser.id}`, JSON.stringify(newUser)));

      // Remove password from user object before storing in state
      const { password: _, ...userWithoutPassword } = newUser;

      try {
        // Ensure we're storing a serializable object
        const userToStore = JSON.parse(JSON.stringify(userWithoutPassword));
        localStorage.setItem('user', JSON.stringify(userToStore));
      } catch (storageError) {
        console.error('Error storing user data in localStorage:', storageError);
        // Continue with registration even if localStorage fails
      }

      setAuthState({
        user: userWithoutPassword as User,
        isAuthenticated: true,
        isLoading: false,
        error: null,
      });

      toast.success('Registration successful');

      // Set a cookie for server-side auth checks
      document.cookie = `user=${JSON.stringify(userWithoutPassword)}; path=/; max-age=86400; SameSite=Strict`;

      // Use window.location.href for more reliable navigation after registration
      window.location.href = '/assessment';
    } catch (error: any) {
      console.error('Registration error:', error);
      setAuthState(prev => ({
        ...prev,
        isLoading: false,
        error: error.message || 'Registration failed',
      }));
      toast.error(error.message || 'Registration failed');
    }
  };

  const logout = () => {
    localStorage.removeItem('user');
    setAuthState({
      user: null,
      isAuthenticated: false,
      isLoading: false,
      error: null,
    });
    toast.success('Logged out successfully');
    router.push('/');
  };

  const checkAuth = async (): Promise<boolean> => {
    if (authState.isAuthenticated && authState.user) {
      return true;
    }

    try {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        let user;
        try {
          user = JSON.parse(storedUser);
        } catch (parseError) {
          console.error('Error parsing user data from localStorage:', parseError);
          localStorage.removeItem('user'); // Remove invalid data
          return false;
        }

        if (!user || !user.id) {
          console.error('Invalid user data structure:', user);
          localStorage.removeItem('user'); // Remove invalid data
          return false;
        }

        // Verify user exists in database
        const dbUser = await safeRedisOperation(() => redis.get(`user:${user.id}`));
        // Check if dbUser exists and is valid
        if (dbUser && (typeof dbUser === 'object' || typeof dbUser === 'string')) {
          // Parse the database user to ensure we have the latest role information
          let parsedDbUser;
          try {
            // Check if dbUser is already an object or needs to be parsed
            if (typeof dbUser === 'object' && !Array.isArray(dbUser)) {
              parsedDbUser = dbUser;
            } else if (typeof dbUser === 'string') {
              parsedDbUser = JSON.parse(dbUser);
            } else {
              throw new Error('Invalid database user data format');
            }
          } catch (parseError) {
            console.error('Error parsing user data from database:', parseError);
            return false;
          }

          if (!parsedDbUser || typeof parsedDbUser !== 'object') {
            console.error('Invalid database user data structure:', parsedDbUser);
            return false;
          }

          if (!parsedDbUser || typeof parsedDbUser !== 'object') {
            console.error('Invalid database user data structure:', parsedDbUser);
            return false;
          }

          // Use the database user's role but keep the user object from localStorage
          // to avoid exposing the password hash
          const updatedUser = {
            ...user,
            role: parsedDbUser.role,
            federation_id: parsedDbUser.federation_id
          };

          setAuthState({
            user: updatedUser,
            isAuthenticated: true,
            isLoading: false,
            error: null,
          });

          // Update localStorage with the latest user data
          try {
            // Ensure we're storing a serializable object
            const userToStore = JSON.parse(JSON.stringify(updatedUser));
            localStorage.setItem('user', JSON.stringify(userToStore));
          } catch (serializeError) {
            console.error('Error serializing user data:', serializeError);
            // Continue with authentication even if storage fails
          }
          return true;
        }
      }
      return false;
    } catch (error) {
      console.error('Auth check error:', error);
      localStorage.removeItem('user'); // Clean up potentially corrupted data
      return false;
    }
  };

  const getCurrentFederationId = () => {
    return authState.user?.federation_id || null;
  };

  const updateUserFederation = async (federationId: string) => {
    if (!authState.user || !authState.user.id) {
      toast.error('You must be logged in to update your federation');
      return false;
    }

    try {
      // Get the current user from the database
      const dbUser = await safeRedisOperation(() => redis.get(`user:${authState.user!.id}`));
      if (!dbUser) {
        toast.error('User not found in database');
        return false;
      }

      // Parse the user data
      let userData;
      try {
        if (typeof dbUser === 'object' && !Array.isArray(dbUser)) {
          userData = dbUser;
        } else if (typeof dbUser === 'string') {
          userData = JSON.parse(dbUser);
        } else {
          throw new Error('Invalid database user data format');
        }
      } catch (parseError) {
        console.error('Error parsing user data from database:', parseError);
        toast.error('Error updating federation');
        return false;
      }

      // Update the federation_id
      userData.federation_id = federationId;
      userData.updated_at = new Date().toISOString();

      // Save the updated user back to the database
      await safeRedisOperation(() => redis.set(`user:${authState.user!.id}`, JSON.stringify(userData)));

      // Also update the user in the users list
      const users = await safeRedisOperation(() => redis.get('users'));
      let userList = [];
      try {
        if (users && typeof users === 'string') {
          userList = JSON.parse(users);
        } else if (users && typeof users === 'object') {
          userList = Array.isArray(users) ? users : [];
        }
      } catch (parseError) {
        console.error('Error parsing users data:', parseError);
      }

      if (Array.isArray(userList)) {
        const updatedUserList = userList.map((u: any) => {
          if (u.id === authState.user!.id) {
            return { ...u, federation_id: federationId, updated_at: new Date().toISOString() };
          }
          return u;
        });

        await safeRedisOperation(() => redis.set('users', JSON.stringify(updatedUserList)));
      }

      // Update the user in the auth state
      const updatedUser = { ...authState.user, federation_id: federationId };
      setAuthState({
        ...authState,
        user: updatedUser,
      });

      // Update localStorage
      try {
        localStorage.setItem('user', JSON.stringify(updatedUser));
      } catch (storageError) {
        console.error('Error storing user data in localStorage:', storageError);
      }

      toast.success('Federation updated successfully');
      return true;
    } catch (error) {
      console.error('Error updating federation:', error);
      toast.error('Failed to update federation');
      return false;
    }
  };

  return (
    <AuthContext.Provider value={{ ...authState, login, register, logout, checkAuth, getCurrentFederationId, updateUserFederation }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
