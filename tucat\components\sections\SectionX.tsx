"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { YesNoQuestionGroup } from "@/components/questions/YesNoQuestionGroup";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionX() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    fee_collection: "",
    financial_management: "",
    financial_planning: "",
    financial_system_quality: "",
    has_cash_system: "",
    uses_accounting_software: "",
    has_chart_accounts: "",
    reconciles_monthly: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 10
    const sectionData = assessments.find(a => a.section_number === 10);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        fee_collection: sectionData.fee_collection || "",
        financial_management: sectionData.financial_management || "",
        financial_planning: sectionData.financial_planning || "",
        financial_system_quality: sectionData.financial_system_quality || "",
        has_cash_system: sectionData.has_cash_system || "",
        uses_accounting_software: sectionData.uses_accounting_software || "",
        has_chart_accounts: sectionData.has_chart_accounts || "",
        reconciles_monthly: sectionData.reconciles_monthly || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please fill out all the questions before saving.");
        return;
      }

      await saveAssessment(10, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  const handleYesNoChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <QuestionCard
      title="FINANCIAL AND ORGANIZATIONAL SUSTAINABILITY"
      bengaliTitle="আর্থিক এবং সাংগঠনিক স্থিতিশীলতা"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the federation's financial management practices and long-term sustainability.",
        bengali: "ফেডারেশনের আর্থিক ব্যবস্থাপনা পদ্ধতি এবং দীর্ঘমেয়াদী স্থিতিশীলতা মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="How well does the federation collect membership fees and generate income from other sources?"
        bengaliQuestion="ফেডারেশন কতটা ভালোভাবে সদস্যপদ ফি সংগ্রহ করে এবং অন্যান্য উৎস থেকে আয় করে?"
        value={formData.fee_collection}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, fee_collection: value }))
        }
        options={[
          {
            value: "1",
            label: "Not well - The federation struggles to collect fees and has no other income.",
            bengaliLabel: "ভালো নয় - ফেডারেশন ফি সংগ্রহে সমস্যায় পড়ে এবং অন্য কোনো আয় নেই।"
          },
          {
            value: "2",
            label: "Somewhat well - Fee collection is inconsistent, and other income is limited.",
            bengaliLabel: "কিছুটা ভালো - ফি সংগ্রহ অনিয়মিত, এবং অন্যান্য আয় সীমিত।"
          },
          {
            value: "3",
            label: "Moderately well - The federation collects fees fairly well and has some other income.",
            bengaliLabel: "মাঝারি ভালো - ফেডারেশন ফি মোটামুটি ভালোভাবে সংগ্রহ করে এবং কিছু আয়ের উৎস রয়েছে।"
          },
          {
            value: "4",
            label: "Very well - The federation collects fees effectively and has strong additional income sources.",
            bengaliLabel: "খুব ভালো - ফেডারেশন ফি কার্যকরভাবে সংগ্রহ করে এবং শক্তিশালী অতিরিক্ত আয়ের উৎস আছে।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={2}
        question="How well does the federation manage its finances according to its constitution?"
        bengaliQuestion="ফেডারেশন তার সংবিধান অনুযায়ী আর্থিক বিষয়গুলি কতটা ভালোভাবে পরিচালনা করে?"
        value={formData.financial_management}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, financial_management: value }))
        }
        options={[
          {
            value: "1",
            label: "Not well - Financial management does not follow the constitution and lacks transparency.",
            bengaliLabel: "ভালো নয় - আর্থিক ব্যবস্থাপনা সংবিধান অনুসরণ করে না এবং স্বচ্ছতার অভাব রয়েছে।"
          },
          {
            value: "2",
            label: "Somewhat well - Financial management partially follows the constitution, with some gaps.",
            bengaliLabel: "কিছুটা ভালো - আর্থিক ব্যবস্থাপনা আংশিকভাবে সংবিধান অনুসরণ করে, কিছু ফাঁক রয়েছে।"
          },
          {
            value: "3",
            label: "Moderately well - Financial management mostly follows the constitution but could be stricter.",
            bengaliLabel: "মাঝারি ভালো - আর্থিক ব্যবস্থাপনা বেশিরভাগ সময় সংবিধান অনুসরণ করে, তবে আরও কঠোর হতে পারে।"
          },
          {
            value: "4",
            label: "Very well - Finances are managed strictly according to the constitution with full transparency.",
            bengaliLabel: "খুব ভালো - আর্থিক বিষয়গুলি সংবিধান অনুযায়ী কঠোরভাবে পরিচালিত হয় এবং পূর্ণ স্বচ্ছতা থাকে।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={3}
        question="How effectively does the federation plan for long-term financial sustainability?"
        bengaliQuestion="ফেডারেশন কীভাবে দীর্ঘমেয়াদী আর্থিক স্থাযিত্বের জন্য কার্যকরভাবে পরিকল্পনা করে"
        value={formData.financial_planning}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, financial_planning: value }))
        }
        options={[
          {
            value: "1",
            label: "Not effectively - There is no clear plan for long-term financial sustainability.",
            bengaliLabel: "কার্যকর নয় - দীর্ঘমেয়াদী আর্থিক স্থাযিত্বের জন্য কোনো সুস্পষ্ট পরিকল্পনা নেই।"
          },
          {
            value: "2",
            label: "Somewhat effective - The plan exists but is underdeveloped or underfunded.",
            bengaliLabel: "কিছুটা কার্যকর - পরিকল্পনা আছে, কিন্তু তা অপরিপক্ব বা অর্থায়নের অভাব রয়েছে।"
          },
          {
            value: "3",
            label: "Moderately effective - There is a plan, but it lacks implementation in some areas.",
            bengaliLabel: "মাঝারি কার্যকর - পরিকল্পনা রয়েছে, তবে কিছু ক্ষেত্রে বাস্তবায়নের অভাব রয়েছে।"
          },
          {
            value: "4",
            label: "Very effective - There is a long-term financial plan with diverse income streams.",
            bengaliLabel: "খুব কার্যকর - দীর্ঘমেয়াদী আর্থিক পরিকল্পনা রয়েছে, যা বৈচিত্র্যপূর্ণ আয়ের উৎসের সাথে সম্পৃক্ত।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={4}
        question="How qualified is the financial system?"
        bengaliQuestion="ফেডারেশনের আর্থিক ব্যবস্থা কতটা দক্ষ ও কার্যকর?"
        value={formData.financial_system_quality}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, financial_system_quality: value }))
        }
        options={[
          {
            value: "1",
            label: "The Federation has no formal financial system",
            bengaliLabel: "কোনো আনুষ্ঠানিক আর্থিক ব্যবস্থা নেই।"
          },
          {
            value: "2",
            label: "The Federation has a basic financial system, but it is incomplete and/or not compliant with accounting standards",
            bengaliLabel: "একটি মৌলিক আর্থিক ব্যবস্থা রয়েছে, তবে এটি অসম্পূর্ণ এবং/অথবা হিসাব মানদণ্ডের সাথে সামঞ্জস্যপূর্ণ নয়।"
          },
          {
            value: "3",
            label: "The Federation has a good financial system with most or all required components",
            bengaliLabel: "ফেডারেশনের একটি ভালো আর্থিক ব্যবস্থা রয়েছে যার বেশিরভাগ বা সমস্ত প্রয়োজনীয় উপাদান রয়েছে।"
          },
          {
            value: "4",
            label: "The organization has a complete and appropriate financial system",
            bengaliLabel: "একটি সম্পূর্ণ এবং উপযুক্ত আর্থিক ব্যবস্থা রয়েছে।"
          }
        ]}
      />

      <div className="border rounded-lg overflow-hidden bg-blue-50/50">
        <div className="p-4 border-b">
          <div className="font-semibold">Financial Systems Assessment</div>
          <div className="text-sm">আর্থিক ব্যবস্থা মূল্যায়ন</div>
        </div>
        <table className="w-full border-collapse">
          <thead className="bg-muted/50">
            <tr className="border-b">
              <th className="p-3 text-left">#</th>
              <th className="p-3 text-left">Question</th>
              <th className="p-3 text-center w-24">Yes</th>
              <th className="p-3 text-center w-24">No</th>
              <th className="p-3 text-center w-24">N/A</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">5.</td>
              <td className="p-3 border-r">
                <div>Does Federation have a cash, accrual or modified system? Are cash payments made?</div>
                <div className="text-sm text-muted-foreground">
                  ফেডারেশনের কি একটি নগদ, ক্রমবর্ধক বা সংশোধিত ব্যবস্থা রয়েছে? নগদ অর্থ প্রদান করা হয় কি?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="has_cash_system"
                      value="yes"
                      checked={formData.has_cash_system === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, has_cash_system: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="has_cash_system"
                      value="no"
                      checked={formData.has_cash_system === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, has_cash_system: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="has_cash_system"
                      value="na"
                      checked={formData.has_cash_system === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, has_cash_system: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">6.</td>
              <td className="p-3 border-r">
                <div>Is your Federation using accounting software?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি হিসাব সফটওয়্যার ব্যবহার করছে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="uses_accounting_software"
                      value="yes"
                      checked={formData.uses_accounting_software === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, uses_accounting_software: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="uses_accounting_software"
                      value="no"
                      checked={formData.uses_accounting_software === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, uses_accounting_software: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="uses_accounting_software"
                      value="na"
                      checked={formData.uses_accounting_software === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, uses_accounting_software: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">7.</td>
              <td className="p-3 border-r">
                <div>Is there a chart of accounts (income and expenses, assets and liabilities)? Does it address donor specific requirements?</div>
                <div className="text-sm text-muted-foreground">
                  আয়ের ও ব্যয়ের, সম্পদ ও দায়ের একটি চার্ট কি আছে? এটি কি দাতার নির্দিষ্ট প্রয়োজনীয়তা সমাধান করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="has_chart_accounts"
                      value="yes"
                      checked={formData.has_chart_accounts === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, has_chart_accounts: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="has_chart_accounts"
                      value="no"
                      checked={formData.has_chart_accounts === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, has_chart_accounts: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="has_chart_accounts"
                      value="na"
                      checked={formData.has_chart_accounts === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, has_chart_accounts: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">8.</td>
              <td className="p-3 border-r">
                <div>Are the bank accounts reconciled monthly against the bank journals/cash books?</div>
                <div className="text-sm text-muted-foreground">
                  ব্যাংক অ্যাকাউন্টগুলা কি মাসিকভাবে ব্যাংক জার্নাল/নগদ বইয়ের বিপরীতে মেলানো হয়?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="reconciles_monthly"
                      value="yes"
                      checked={formData.reconciles_monthly === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, reconciles_monthly: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="reconciles_monthly"
                      value="no"
                      checked={formData.reconciles_monthly === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, reconciles_monthly: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="reconciles_monthly"
                      value="na"
                      checked={formData.reconciles_monthly === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, reconciles_monthly: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </QuestionCard>
  );
}