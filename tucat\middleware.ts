import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

export function middleware(request: NextRequest) {
  const path = request.nextUrl.pathname;

  // Define public paths that don't require authentication
  const isPublicPath = path === '/' || path === '/login';

  // Special case for register - we always want to allow access to this page
  const isRegisterPath = path === '/register';

  // Define protected paths that require authentication
  const isProtectedPath = path.startsWith('/assessment') || path.startsWith('/dashboard');

  // Get the authentication token from the cookies
  const user = request.cookies.get('user')?.value;

  // For debugging - log the path and user status
  console.log(`Middleware: Path=${path}, User exists=${!!user}, Public path=${isPublicPath}, Register path=${isRegisterPath}`);

  // Always allow access to the register page
  if (isRegisterPath) {
    return NextResponse.next();
  }

  // Redirect logic for other paths
  if (isPublicPath && user) {
    // If user is already logged in and tries to access public paths, redirect to assessment
    // Add a check to prevent redirect loops by checking the referer
    const referer = request.headers.get('referer') || '';
    if (referer.includes('/assessment')) {
      // If coming from assessment, don't redirect to prevent loops
      return NextResponse.next();
    }
    return NextResponse.redirect(new URL('/assessment', request.url));
  }

  if (isProtectedPath && !user) {
    // If user is not logged in and tries to access protected paths, redirect to login
    // Store the original URL to redirect back after login
    const url = new URL('/login', request.url);
    return NextResponse.redirect(url);
  }

  return NextResponse.next();
}

// Configure the middleware to run on specific paths
export const config = {
  matcher: [
    '/',
    '/login',
    '/register',
    '/assessment/:path*',
    '/dashboard/:path*',
  ],
};