"use client";

interface YesNoQuestionProps {
  questionNumber: number;
  question: string;
  bengaliQuestion: string;
  name: string;
  value: string;
  onChange: (value: string) => void;
}

export function YesNoQuestion({
  questionNumber,
  question,
  bengaliQuestion,
  name,
  value,
  onChange,
}: YesNoQuestionProps) {
  return (
    <tr className="border-b">
      <td className="p-3 w-12 align-top">{questionNumber}.</td>
      <td className="p-3 border-r">
        <div>{question}</div>
        <div className="text-sm text-muted-foreground">
          {bengaliQuestion}
        </div>
      </td>
      <td colSpan={3} className="p-3">
        <div className="flex justify-center gap-12">
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name={name}
              value="yes"
              checked={value === "yes"}
              onChange={(e) => onChange(e.target.value)}
              className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
            />
            <span>Yes</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name={name}
              value="no"
              checked={value === "no"}
              onChange={(e) => onChange(e.target.value)}
              className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
            />
            <span>No</span>
          </label>
          <label className="flex items-center gap-2 cursor-pointer">
            <input
              type="radio"
              name={name}
              value="na"
              checked={value === "na"}
              onChange={(e) => onChange(e.target.value)}
              className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
            />
            <span>N/A</span>
          </label>
        </div>
      </td>
    </tr>
  );
}