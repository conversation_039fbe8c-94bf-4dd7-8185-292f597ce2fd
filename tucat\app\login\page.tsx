"use client";

import { useState, useEffect, useCallback } from 'react';
import { useAuth } from '@/components/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import Link from 'next/link';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Checkbox } from '@/components/ui/checkbox';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { Skeleton } from '@/components/ui/skeleton';
import ErrorBoundary from '@/components/ErrorBoundary';
import {
  LogIn,
  AlertCircle,
  Mail,
  Lock,
  Eye,
  EyeOff,
  Building,
  ArrowRight,
} from 'lucide-react';
import { cn } from '@/lib/utils';
import { useRouter } from 'next/navigation';

export default function LoginPage() {
  const { login, isLoading, error, checkAuth } = useAuth();
  const router = useRouter();

  // Consolidated form state
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    showPassword: false,
    rememberMe: false,
    formError: null as string | null,
  });

  const [isPageReady, setIsPageReady] = useState(false);

  // Handle input changes with a single function
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
      formError: null, // Clear form error when user types
    }));
  };

  // Optimized authentication check and redirect
  const checkUserAuthentication = useCallback(async () => {
    try {
      const isAuthenticated = await checkAuth();

      if (isAuthenticated) {
        router.push('/assessment');
      }
    } catch (error) {
      console.error('Auth check error:', error);
      // Silent fail, already handled in AuthContext
    } finally {
      setIsPageReady(true);
    }
  }, [checkAuth, router]);

  // Check authentication on mount
  useEffect(() => {
    checkUserAuthentication();
  }, [checkUserAuthentication]);

  const validateForm = () => {
    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (!formData.email) {
      setFormData(prev => ({ ...prev, formError: 'Please enter your email address' }));
      return false;
    }
    if (!emailRegex.test(formData.email)) {
      setFormData(prev => ({ ...prev, formError: 'Invalid email format. Please enter a valid email address (e.g., <EMAIL>)' }));
      return false;
    }
    if (!formData.password) {
      setFormData(prev => ({ ...prev, formError: 'Please enter your password' }));
      return false;
    }
    if (formData.password.length < 6) {
      setFormData(prev => ({ ...prev, formError: 'Password must be at least 6 characters long' }));
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateForm()) {
      try {
        await login(formData.email, formData.password);
        // The login function now handles the redirect to /assessment
      } catch (error) {
        // Error is already handled in the login function
        console.error('Login submission error:', error);
      }
    }
  };

  // Loading state UI
  if (!isPageReady) {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <Card className="w-full max-w-md border-t-4 border-t-primary shadow-lg">
          <CardHeader className="space-y-2">
            <CardTitle className="text-2xl font-bold">Loading...</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
            <Skeleton className="h-10 w-full" />
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <ErrorBoundary>
      <div className="flex min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-200">
        {/* Theme toggle in the top right corner */}
        <div className="absolute top-4 right-4 z-10">
          <ThemeToggle />
        </div>

        {/* Left side - Illustration/Brand section */}
        <div className="hidden lg:flex lg:w-1/2 bg-primary/10 dark:bg-primary/5 flex-col items-center justify-center p-12 relative overflow-hidden">
          <div className="flex flex-col items-center justify-center space-y-8 z-10 opacity-100 translate-y-0 transition-all duration-500">
            <div className="rounded-full bg-primary/20 p-6 backdrop-blur-sm">
              <Building className="h-16 w-16 text-primary" />
            </div>
            <div className="space-y-4 text-center">
              <h1 className="text-4xl font-bold tracking-tight">Trade Union Assessment</h1>
              <p className="text-lg text-muted-foreground max-w-md">
                Access your federation dashboard and manage your assessment progress
              </p>
            </div>
          </div>
        </div>

        {/* Right side - Login form */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-4 md:p-8">
          <Card className="w-full max-w-md border-t-4 border-t-primary transition-all duration-500 shadow-lg hover:shadow-xl opacity-100 translate-y-0">
            <CardHeader className="space-y-2">
              <div className="flex justify-between items-center">
                <CardTitle className="text-2xl font-bold">Welcome Back</CardTitle>
                <LogIn className="h-6 w-6 text-primary" />
              </div>
              <CardDescription className="text-base">
                Sign in to your account to continue
              </CardDescription>
            </CardHeader>

            <form onSubmit={handleSubmit}>
              <CardContent className="space-y-4">
                {/* Error display */}
                {(error || formData.formError) && (
                  <Alert variant="destructive" className="text-sm animate-fadeIn">
                    <AlertCircle className="h-4 w-4" />
                    <AlertDescription className="font-medium">{error || formData.formError}</AlertDescription>
                  </Alert>
                )}

                {/* Email input with icon */}
                <div className="space-y-2">
                  <Label htmlFor="email" className="text-sm font-medium">
                    Email Address
                  </Label>
                  <div className="relative">
                    <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="email"
                      name="email"
                      type="email"
                      placeholder="<EMAIL>"
                      value={formData.email}
                      onChange={handleInputChange}
                      className="pl-10 transition-all duration-200 border-muted-foreground/20 focus:border-primary"
                    />
                  </div>
                </div>

                {/* Password input with icon and toggle */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between">
                    <Label htmlFor="password" className="text-sm font-medium">Password</Label>
                    <Link
                      href="/forgot-password"
                      className="text-xs text-primary hover:underline transition-colors"
                    >
                      Forgot password?
                    </Link>
                  </div>
                  <div className="relative">
                    <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="password"
                      name="password"
                      type={formData.showPassword ? "text" : "password"}
                      value={formData.password}
                      onChange={handleInputChange}
                      className="pl-10 pr-10 transition-all duration-200 border-muted-foreground/20 focus:border-primary"
                    />
                    <button
                      type="button"
                      onClick={() => setFormData(prev => ({ ...prev, showPassword: !prev.showPassword }))}
                      className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                      tabIndex={-1}
                    >
                      {formData.showPassword ? (
                        <EyeOff className="h-4 w-4" />
                      ) : (
                        <Eye className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>

                {/* Remember me checkbox */}
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="rememberMe"
                    name="rememberMe"
                    checked={formData.rememberMe}
                    onCheckedChange={(checked) =>
                      setFormData(prev => ({
                        ...prev,
                        rememberMe: checked === true
                      }))
                    }
                  />
                  <Label
                    htmlFor="rememberMe"
                    className="text-sm font-medium cursor-pointer"
                  >
                    Remember me
                  </Label>
                </div>
              </CardContent>

              <CardFooter className="flex flex-col space-y-6">
                {/* Login button */}
                <Button
                  type="submit"
                  className="w-full h-11 transition-all duration-200 hover:shadow-md flex items-center justify-center gap-2 text-base"
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <>
                      <div className="h-4 w-4 rounded-full border-2 border-t-transparent border-primary/50 animate-spin" />
                      Signing in...
                    </>
                  ) : (
                    <>
                      Sign In
                      <ArrowRight className="h-4 w-4 ml-1" />
                    </>
                  )}
                </Button>

                {/* Register link */}
                <div className="text-center text-sm">
                  Don&apos;t have an account?{' '}
                  <Link href="/register" className="font-medium text-primary hover:underline transition-colors">
                    Create an account
                  </Link>
                </div>
              </CardFooter>
            </form>
          </Card>
        </div>
      </div>
    </ErrorBoundary>
  );
}