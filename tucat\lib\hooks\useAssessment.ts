import { useState, useEffect, useCallback } from 'react';
import { redis, safeRedisOperation } from '@/lib/upstash';
import { Federation, Committee, Assessment, AssessmentScore, SCORE_MESSAGES } from '@/lib/types';
import { useAuth } from '@/components/AuthContext';

export interface AssessmentState {
  federation: Partial<Federation>;
  committees: Partial<Committee>[];
  assessments: Partial<Assessment>[];
  score: AssessmentScore | null;
  currentSection: number;
  isLoading: boolean;
  error: string | null;
  allFederations: Partial<Federation>[];
}

export function useAssessment() {
  const { user, isAuthenticated } = useAuth();
  const [state, setState] = useState<AssessmentState>({
    federation: {},
    committees: [],
    assessments: [],
    score: null,
    currentSection: 1,
    isLoading: false,
    error: null,
    allFederations: [],
  });



  const loadAssessment = useCallback(async (federationId?: string) => {
    setState((prevState) => ({ ...prevState, isLoading: true, error: null }));
    try {
      const idToLoad = federationId || user?.federation_id;
      if (!idToLoad) {
        throw new Error('No federation ID provided or available.');
      }

      const fetchedFederation = await safeRedisOperation<Federation>(
        async () => await redis.hgetall(`federation:${idToLoad}`)
      );
      if (!fetchedFederation) {
        throw new Error('Federation not found.');
      }

      const fetchedCommittees = await safeRedisOperation<Committee[]>(async () => {
        const committeeKeys = await redis.lrange(`federation:${idToLoad}:committees`, 0, -1);
        const committeesData = await Promise.all(
          committeeKeys.map(async (key) => await redis.hgetall(key))
        );
        return committeesData.filter(Boolean) as Committee[];
      });

      const fetchedAssessments = await safeRedisOperation<Assessment[]>(async () => {
        const assessmentKeys = await redis.lrange(`federation:${idToLoad}:assessments`, 0, -1);
        const assessmentsData = await Promise.all(
          assessmentKeys.map(async (key) => await redis.hgetall(key))
        );
        return assessmentsData.filter(Boolean) as Assessment[];
      });

      setState((prevState) => ({
        ...prevState,
        federation: fetchedFederation,
        committees: fetchedCommittees,
        assessments: fetchedAssessments,
        isLoading: false,
      }));
    } catch (err: any) {
      console.error('Failed to load assessment:', err);
      setState((prevState) => ({ ...prevState, error: err.message, isLoading: false }));
    }
  }, [user?.federation_id]);

  const getAllFederations = useCallback(async () => {
    setState((prevState) => ({ ...prevState, isLoading: true, error: null }));
    try {
      const federationKeys = await safeRedisOperation<string[]>(async () => {
        return await redis.keys('federation:*');
      });

      const federationsData = await Promise.all(
        federationKeys.map(async (key) => {
          if (key.includes(':committees') || key.includes(':assessments')) {
            return null; // Skip committee and assessment keys
          }
          return await safeRedisOperation<Federation>(async () => await redis.hgetall(key));
        })
      );
      const filteredFederations = federationsData.filter(Boolean) as Federation[];
      setState((prevState) => ({ ...prevState, allFederations: filteredFederations, isLoading: false }));
    } catch (err: any) {
      console.error('Failed to load all federations:', err);
      setState((prevState) => ({ ...prevState, error: err.message, isLoading: false }));
    }
  }, []);

  // Load user's federation data when authenticated
  useEffect(() => {
    if (isAuthenticated) {
      if (user?.federation_id) {
        loadAssessment(user.federation_id);
      }
      getAllFederations(); // Fetch all federations for admin dashboard
    } else {
      // Clear state when user is not authenticated
      setState({
        federation: {},
        committees: [],
        assessments: [],
        score: null,
        currentSection: 1,
        isLoading: false,
        error: null,
        allFederations: [],
      });
    }
  }, [isAuthenticated, user?.federation_id, loadAssessment, getAllFederations]);

  const calculateScore = () => {
    let total = 0;
    let maxPossible = 0;

    // Create a copy of assessments to update with scores
    const updatedAssessments = [...state.assessments];

    // Calculate score for each section separately
    updatedAssessments.forEach((assessment, index) => {
      let sectionScore = 0;
      let sectionMaxScore = 0;

      // Section III scoring
      if (assessment.section_number === 3) {
        sectionMaxScore = 14;

        if (assessment.vision_mission_status === '4') sectionScore += 3;
        else if (assessment.vision_mission_status === '3') sectionScore += 2;
        else if (assessment.vision_mission_status === '2') sectionScore += 1;

        // Yes/No/NA questions scoring
        if (assessment.vision_posted === 'yes') sectionScore += 1;
        if (assessment.vision_in_documents === 'yes') sectionScore += 1;
        if (assessment.vision_for_planning === 'yes') sectionScore += 1;

        if (assessment.decision_making === 'a') sectionScore += 3;
        else if (assessment.decision_making === 'b') sectionScore += 2;
        else if (assessment.decision_making === 'c') sectionScore += 1;

        if (assessment.emerging_issues_handling === 'a') sectionScore += 3;
        else if (assessment.emerging_issues_handling === 'b') sectionScore += 2;
        else if (assessment.emerging_issues_handling === 'c') sectionScore += 1;

        if (assessment.leadership_development_plan === 'a') sectionScore += 2;
        else if (assessment.leadership_development_plan === 'b') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section IV scoring
      if (assessment.section_number === 4) {
        sectionMaxScore = 4;

        if (assessment.organizational_structure === 'a') sectionScore += 2;
        else if (assessment.organizational_structure === 'b') sectionScore += 1;

        if (assessment.roles_defined === 'a') sectionScore += 2;
        else if (assessment.roles_defined === 'b') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section V scoring
      if (assessment.section_number === 5) {
        sectionMaxScore = 11;

        if (assessment.management_approach === 'a') sectionScore += 2;
        else if (assessment.management_approach === 'c') sectionScore += 1;
        else if (assessment.management_approach === 'd') sectionScore += 0;

        if (assessment.authority_delegation === 'a') sectionScore += 2;
        else if (assessment.authority_delegation === 'c') sectionScore += 1;

        if (assessment.decision_making_process === '4') sectionScore += 3;
        else if (assessment.decision_making_process === '3') sectionScore += 2;
        else if (assessment.decision_making_process === '2') sectionScore += 1;

        if (assessment.deputy_availability === 'a') sectionScore += 2;
        else if (assessment.deputy_availability === 'b') sectionScore += 1;

        if (assessment.transition_plan === 'a') sectionScore += 2;
        else if (assessment.transition_plan === 'b') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section VI scoring
      if (assessment.section_number === 6) {
        sectionMaxScore = 9;

        if (assessment.workers_involvement_level === '4') sectionScore += 4;
        else if (assessment.workers_involvement_level === '3') sectionScore += 3;
        else if (assessment.workers_involvement_level === '2') sectionScore += 2;
        else if (assessment.workers_involvement_level === '1') sectionScore += 1;

        if (assessment.involve_program_activities === 'yes') sectionScore += 1;
        if (assessment.involve_leaders_orientation === 'yes') sectionScore += 1;
        if (assessment.solicit_feedback === 'yes') sectionScore += 1;
        if (assessment.regular_interaction === 'yes') sectionScore += 1;
        if (assessment.share_results === 'yes') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section VII scoring
      if (assessment.section_number === 7) {
        sectionMaxScore = 7;

        if (assessment.cultural_gender_consideration === '4') sectionScore += 3;
        else if (assessment.cultural_gender_consideration === '3') sectionScore += 2;
        else if (assessment.cultural_gender_consideration === '2') sectionScore += 1;

        if (assessment.consider_local_culture === 'yes') sectionScore += 1;
        if (assessment.documented_guidelines === 'yes') sectionScore += 1;
        if (assessment.provide_training === 'yes') sectionScore += 1;
        if (assessment.use_assessment_findings === 'yes') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section VIII scoring
      if (assessment.section_number === 8) {
        sectionMaxScore = 8;

        if (assessment.representation_effectiveness === '4') sectionScore += 3;
        else if (assessment.representation_effectiveness === '3') sectionScore += 2;
        else if (assessment.representation_effectiveness === '2') sectionScore += 1;

        if (assessment.member_involvement === '4') sectionScore += 3;
        else if (assessment.member_involvement === '3') sectionScore += 2;
        else if (assessment.member_involvement === '2') sectionScore += 1;

        if (assessment.bargaining_strategy === 'a') sectionScore += 2;
        else if (assessment.bargaining_strategy === 'b') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section IX scoring
      if (assessment.section_number === 9) {
        sectionMaxScore = 11;

        if (assessment.communication_effectiveness === '4') sectionScore += 3;
        else if (assessment.communication_effectiveness === '3') sectionScore += 2;
        else if (assessment.communication_effectiveness === '2') sectionScore += 1;

        if (assessment.member_engagement === '4') sectionScore += 3;
        else if (assessment.member_engagement === '3') sectionScore += 2;
        else if (assessment.member_engagement === '2') sectionScore += 1;

        if (assessment.participation_opportunities === '4') sectionScore += 3;
        else if (assessment.participation_opportunities === '3') sectionScore += 2;
        else if (assessment.participation_opportunities === '2') sectionScore += 1;

        if (assessment.feedback_collection === 'a') sectionScore += 2;
        else if (assessment.feedback_collection === 'b') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section X scoring
      if (assessment.section_number === 10) {
        sectionMaxScore = 16;

        if (assessment.fee_collection === '4') sectionScore += 3;
        else if (assessment.fee_collection === '3') sectionScore += 2;
        else if (assessment.fee_collection === '2') sectionScore += 1;

        if (assessment.financial_management === '4') sectionScore += 3;
        else if (assessment.financial_management === '3') sectionScore += 2;
        else if (assessment.financial_management === '2') sectionScore += 1;

        if (assessment.financial_planning === '4') sectionScore += 3;
        else if (assessment.financial_planning === '3') sectionScore += 2;
        else if (assessment.financial_planning === '2') sectionScore += 1;

        if (assessment.financial_system_quality === '4') sectionScore += 3;
        else if (assessment.financial_system_quality === '3') sectionScore += 2;
        else if (assessment.financial_system_quality === '2') sectionScore += 1;

        if (assessment.has_cash_system === 'yes') sectionScore += 1;
        if (assessment.uses_accounting_software === 'yes') sectionScore += 1;
        if (assessment.has_chart_accounts === 'yes') sectionScore += 1;
        if (assessment.reconciles_monthly === 'yes') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Section XI scoring
      if (assessment.section_number === 11) {
        sectionMaxScore = 10;

        if (assessment.audit_system_quality === '4') sectionScore += 3;
        else if (assessment.audit_system_quality === '3') sectionScore += 2;
        else if (assessment.audit_system_quality === '2') sectionScore += 1;

        if (assessment.requires_annual_audit === 'yes') sectionScore += 1;
        if (assessment.regularly_audited === 'yes') sectionScore += 1;
        if (assessment.auditor_selection === 'yes') sectionScore += 1;
        if (assessment.audit_manager === 'yes') sectionScore += 1;
        if (assessment.implements_recommendations === 'yes') sectionScore += 1;
        if (assessment.shares_reports === 'yes') sectionScore += 1;
        if (assessment.report_provides_info === 'yes') sectionScore += 1;

        // Add to total scores
        total += sectionScore;
        maxPossible += sectionMaxScore;
      }

      // Calculate and store the section score (out of 10)
      if (sectionMaxScore > 0) {
        const normalizedScore = Math.round((sectionScore / sectionMaxScore) * 10);
        // Update the assessment with the calculated score
        updatedAssessments[index] = {
          ...assessment,
          score: normalizedScore
        };
      }
    });

    const percentage = (total / maxPossible) * 100;
    let message = SCORE_MESSAGES.needsImprovement;

    if (percentage >= 90) message = SCORE_MESSAGES.excellent;
    else if (percentage >= 75) message = SCORE_MESSAGES.good;
    else if (percentage >= 50) message = SCORE_MESSAGES.fair;

    const score = {
      total,
      maxPossible,
      percentage,
      message
    };

    // Format percentage to be a rounded number
    const formattedScore = {
      ...score,
      percentage: Math.round(score.percentage)
    };

    // Check if the assessments have actually changed to avoid unnecessary updates
    const scoresChanged = state.assessments.some((assessment, index) => {
      const updatedAssessment = updatedAssessments[index];
      return updatedAssessment && assessment.score !== updatedAssessment.score;
    });

    // Only update state if scores have changed or if there's no existing score
    if (scoresChanged || !state.score) {
      // Use setState outside of render cycle
      setState(prev => ({
        ...prev,
        score: formattedScore,
        assessments: updatedAssessments
      }));

      // Save the updated assessments with scores to Redis
      if (user?.federation_id) {
        safeRedisOperation(() =>
          redis.set(`assessments:${user.federation_id}`, JSON.stringify(updatedAssessments))
        );
      }
    }

    return score;
  };

  const saveFederation = async (data: Partial<Federation>) => {
    setState(prev => ({ ...prev, isLoading: true, error: null }));
    try {
      // Always prioritize the authenticated user's federation_id for data isolation
      let federationId = user?.federation_id || state.federation.id || data.id;

      if (!federationId) {
        // If no federation_id is available, generate a new one
        // This should rarely happen as users should always have a federation_id
        federationId = `fed_${Date.now()}`;
        console.warn('Creating federation without user federation_id');
      }

      const federationData = {
        name: data.name || '',
        federation_type: data.federation_type || '',
        establishment_year: Number(data.establishment_year) || 0,
        president_name: data.president_name || '',
        president_age: Number(data.president_age) || 0,
        secretary_name: data.secretary_name || '',
        secretary_age: Number(data.secretary_age) || 0,
        youth_percentage: data.youth_percentage || '0-25%',
        created_at: state.federation.created_at || new Date().toISOString(),
        updated_at: new Date().toISOString(),
        ...data,  // Spread data after setting defaults to ensure user values take precedence
        id: federationId  // Ensure ID is always set correctly after data spread
      };

      await safeRedisOperation(() =>
        redis.set(`federation:${federationId}`, JSON.stringify(federationData))
      );

      setState(prev => ({
        ...prev,
        federation: federationData,
        isLoading: false,
      }));
      return federationData;
    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to save federation data',
      }));
      return null;
    }
  };

  const saveCommittees = async (committees: Partial<Committee>[]) => {
    // Always prioritize the authenticated user's federation_id for data isolation
    let federationId = user?.federation_id || state.federation.id;

    // If federation ID is still not available, check if we have a federation object with an ID
    if (!federationId && state.federation && state.federation.id) {
      federationId = state.federation.id;
    }

    // If we still don't have a federation ID, check if there's one in the first committee
    if (!federationId && committees.length > 0 && committees[0].federation_id) {
      federationId = committees[0].federation_id;
    }

    if (!federationId) {
      console.error('No federation ID available for saving committees');
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Missing federation ID',
      }));
      return null;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const committeeData = committees.filter(c => c.name).map(committee => ({
        id: `com_${Date.now()}_${Math.random().toString(36).slice(2)}`,
        federation_id: federationId,
        name: committee.name || '',
        total_members: Number(committee.total_members) || 0,
        male_members: Number(committee.male_members) || 0,
        female_members: Number(committee.female_members) || 0,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      if (committeeData.length) {
        await safeRedisOperation(() =>
          redis.set(`committees:${federationId}`, JSON.stringify(committeeData))
        );
      }

      setState(prev => ({
        ...prev,
        committees: committeeData,
        isLoading: false,
      }));
      return committeeData;
    } catch (error) {
      console.error('Failed to save committee data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to save committee data',
      }));
      return null;
    }
  };

  const saveAssessment = async (sectionNumber: number, data: Partial<Assessment>) => {
    // Always prioritize the authenticated user's federation_id for data isolation
    let federationId = user?.federation_id || state.federation.id;

    // If federation ID is still not available, check if we have a federation object with an ID
    if (!federationId && state.federation && state.federation.id) {
      federationId = state.federation.id;
    }

    // If we still don't have a federation ID, check if there's one in the data
    if (!federationId && data.federation_id) {
      federationId = data.federation_id;
    }

    if (!federationId) {
      console.error('No federation ID available for saving assessment');
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Missing federation ID',
      }));
      return null;
    }

    setState(prev => ({ ...prev, isLoading: true, error: null }));

    try {
      const assessmentData = {
        id: `ass_${Date.now()}_${Math.random().toString(36).slice(2)}`,
        federation_id: federationId,
        section_number: sectionNumber,
        ...data,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };

      await safeRedisOperation(() =>
        redis.set(
          `assessment:${federationId}:${sectionNumber}`,
          JSON.stringify(assessmentData)
        )
      );

      setState(prev => ({
        ...prev,
        assessments: [
          ...prev.assessments.filter(a => a.section_number !== sectionNumber),
          assessmentData
        ],
        isLoading: false,
      }));
      return assessmentData;
    } catch (error) {
      console.error('Failed to save assessment data:', error);
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: 'Failed to save assessment data',
      }));
      return null;
    }
  };

  return {
    ...state,
    saveFederation,
    saveCommittees,
    saveAssessment,
    loadAssessment,
    calculateScore,
    setCurrentSection: (section: number) =>
      setState(prev => ({ ...prev, currentSection: section })),
  };
}