"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionVIII() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    representation_effectiveness: "",
    member_involvement: "",
    bargaining_strategy: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 8
    const sectionData = assessments.find(a => a.section_number === 8);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        representation_effectiveness: sectionData.representation_effectiveness || "",
        member_involvement: sectionData.member_involvement || "",
        bargaining_strategy: sectionData.bargaining_strategy || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(8, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  return (
    <QuestionCard
      title="COLLECTIVE BARGAINING AND ADVOCACY"
      bengaliTitle="যৌথ দরকষাকষি এবং অ্যাডভোকেসি"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the federation's effectiveness in representing members' interests and conducting collective bargaining.",
        bengali: "সদস্যদের স্বার্থ প্রতিনিধিত্ব এবং যৌথ দরকষাকষি পরিচালনায় ফেডারেশনের কার্যকারিতা মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="How effective is the federation in representing members' interests during collective bargaining?"
        bengaliQuestion="ফেডারেশন কতটা কার্যকরভাবে তার সদস্যদের স্বার্থে যোগাযোগ করে?"
        value={formData.representation_effectiveness}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, representation_effectiveness: value }))
        }
        options={[
          {
            value: "1",
            label: "Not effective - The federation struggles to represent members effectively",
            bengaliLabel: "অকার্যকর - সদস্যদের স্বার্থে নিয়মিত ভাবে কোনো যোগাযোগ নেই।"
          },
          {
            value: "2",
            label: "Somewhat effective - The federation is involved in negotiations but has limited impact",
            bengaliLabel: "কিছুটা কার্যকর - যোগাযোগ অনিয়মিত এবং সকল সদস্যদের সাথে দেখা হয় না।"
          },
          {
            value: "3",
            label: "Moderately effective - The federation achieves some successes but could do more",
            bengaliLabel: "মাঝারি কার্যকর - যোগাযোগ ভালো, তবে সদস্যদের সাথে যোগাযোগ আরো উন্নত করা যেতে পারে।"
          },
          {
            value: "4",
            label: "Very effective - The federation consistently secures favorable outcomes for members",
            bengaliLabel: "খুব কার্যকর - যোগাযোগ নিয়মিত, বৃহৎ এবং সকল সদস্যদের কাছে পৌঁছে।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={2}
        question="How well does the labor federation involve its members in supporting their rights and negotiating better deals?"
        bengaliQuestion="শ্রমিক ফেডারেশন তার সদস্যদের অধিকারের জন্য কাজ করতে এবং ভালো চুক্তি করতে কতটা যুক্ত করে?"
        value={formData.member_involvement}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, member_involvement: value }))
        }
        options={[
          {
            value: "1",
            label: "Not well - Members are not involved in advocacy and bargaining efforts",
            bengaliLabel: "ভালো নয় - সদস্যরা পক্ষসমর্থন এবং দরকষাকষির প্রচেষ্টায় যুক্ত নয়।"
          },
          {
            value: "2",
            label: "Somewhat well - Engagement is sporadic and limited",
            bengaliLabel: "কিছুটা ভালো - সদস্যরা পক্ষসমর্থন এবং দরকষাকষির প্রচেষ্টায় যুক্ত নয়।"
          },
          {
            value: "3",
            label: "Moderately well - Some members are engaged, but engagement could be stronger",
            bengaliLabel: "মাঝামাঝি ভালো - কিছু সদস্য যুক্ত, তবে সম্পৃক্ততা আরও শক্তিশালী হতে পারে।"
          },
          {
            value: "4",
            label: "Very well - Members are actively engaged and informed in all efforts",
            bengaliLabel: "খুব ভালো - সদস্যরা সমস্ত প্রচেষ্টায় সক্রিয়ভাবে সম্পৃক্ত এবং অবগত।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={3}
        question="Does the federation have a strategy to improve its collective bargaining power?"
        bengaliQuestion="ফেডারেশনের কি তার যৌথ দরকষাকষির ক্ষমতা বাড়ানোর জন্য কোনো কৌশল রয়েছে?"
        value={formData.bargaining_strategy}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, bargaining_strategy: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, a comprehensive strategy is in place",
            bengaliLabel: "হ্যাঁ, একটি বিস্তৃত কৌশল প্রণয়ন করা হয়েছে।"
          },
          {
            value: "b",
            label: "Somewhat – There is a strategy but it lacks depth or focus",
            bengaliLabel: "কিছু – একটি কৌশল রয়েছে কিন্তু এতে গভীরতা বা ফোকাসের অভাব রয়েছে।"
          },
          {
            value: "c",
            label: "No, there is no clear strategy in place to strengthen collective bargaining power",
            bengaliLabel: "না, যৌথ দরকষাকষির ক্ষমতা বাড়ানোর জন্য কোনো স্পষ্ট কৌশল নেই।"
          }
        ]}
      />
    </QuestionCard>
  );
}