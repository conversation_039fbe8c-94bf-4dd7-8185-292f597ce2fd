"use client";

import { YesNoQuestion } from "./YesNoQuestion";
import { cn } from "@/lib/utils";
import { Info } from "lucide-react";

interface Question {
  number: number;
  question: string;
  bengaliQuestion: string;
  name: string;
}

interface YesNoQuestionGroupProps {
  title: string;
  bengaliTitle: string;
  questions: Question[];
  values: Record<string, string>;
  onChange: (name: string, value: string) => void;
}

export function YesNoQuestionGroup({
  title,
  bengaliTitle,
  questions,
  values,
  onChange,
}: YesNoQuestionGroupProps) {
  return (
    <div className="border rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-all duration-200 bg-gradient-to-r from-blue-50/50 to-blue-50/30 dark:from-blue-900/10 dark:to-blue-900/5">
      <div className="p-4 border-b flex items-start gap-2">
        <Info className="h-5 w-5 text-blue-500 dark:text-blue-400 mt-0.5 flex-shrink-0" />
        <div>
          <div className="font-semibold text-blue-700 dark:text-blue-300">{title}</div>
          <div className="text-sm text-blue-600/80 dark:text-blue-400/80">{bengaliTitle}</div>
        </div>
      </div>

      {/* Desktop view */}
      <div className="hidden md:block">
        <table className="w-full border-collapse">
          <thead className="bg-muted/50">
            <tr className="border-b">
              <th className="p-3 text-left">#</th>
              <th className="p-3 text-left">Question</th>
              <th className="p-3 text-center w-24">Yes</th>
              <th className="p-3 text-center w-24">No</th>
              <th className="p-3 text-center w-24">N/A</th>
            </tr>
          </thead>
          <tbody>
            {questions.map((q) => (
              <YesNoQuestion
                key={q.name}
                questionNumber={q.number}
                question={q.question}
                bengaliQuestion={q.bengaliQuestion}
                name={q.name}
                value={values[q.name] || ""}
                onChange={(value) => onChange(q.name, value)}
              />
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile view */}
      <div className="md:hidden">
        {questions.map((q) => (
          <div key={q.name} className="border-b last:border-b-0 p-4">
            <div className="flex items-start gap-2 mb-3">
              <span className="bg-primary/10 text-primary font-medium rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0">
                {q.number}
              </span>
              <div>
                <p className="font-medium">{q.question}</p>
                <p className="text-sm text-muted-foreground mt-1">{q.bengaliQuestion}</p>
              </div>
            </div>
            <div className="flex justify-center gap-4 mt-3">
              <label className={cn(
                "flex items-center gap-2 cursor-pointer border rounded-md px-3 py-2 transition-colors",
                values[q.name] === "yes" ? "bg-green-100 border-green-300 dark:bg-green-900/30 dark:border-green-700" : "border-gray-200 dark:border-gray-700"
              )}>
                <input
                  type="radio"
                  name={q.name}
                  value="yes"
                  checked={values[q.name] === "yes"}
                  onChange={() => onChange(q.name, "yes")}
                  className="sr-only"
                />
                <span className={cn(
                  "w-4 h-4 rounded-full border flex items-center justify-center",
                  values[q.name] === "yes" ? "border-green-500 bg-green-500" : "border-gray-300 dark:border-gray-600"
                )}>
                  {values[q.name] === "yes" && <span className="w-2 h-2 rounded-full bg-white"></span>}
                </span>
                <span>Yes</span>
              </label>
              <label className={cn(
                "flex items-center gap-2 cursor-pointer border rounded-md px-3 py-2 transition-colors",
                values[q.name] === "no" ? "bg-red-100 border-red-300 dark:bg-red-900/30 dark:border-red-700" : "border-gray-200 dark:border-gray-700"
              )}>
                <input
                  type="radio"
                  name={q.name}
                  value="no"
                  checked={values[q.name] === "no"}
                  onChange={() => onChange(q.name, "no")}
                  className="sr-only"
                />
                <span className={cn(
                  "w-4 h-4 rounded-full border flex items-center justify-center",
                  values[q.name] === "no" ? "border-red-500 bg-red-500" : "border-gray-300 dark:border-gray-600"
                )}>
                  {values[q.name] === "no" && <span className="w-2 h-2 rounded-full bg-white"></span>}
                </span>
                <span>No</span>
              </label>
              <label className={cn(
                "flex items-center gap-2 cursor-pointer border rounded-md px-3 py-2 transition-colors",
                values[q.name] === "na" ? "bg-gray-100 border-gray-300 dark:bg-gray-800 dark:border-gray-700" : "border-gray-200 dark:border-gray-700"
              )}>
                <input
                  type="radio"
                  name={q.name}
                  value="na"
                  checked={values[q.name] === "na"}
                  onChange={() => onChange(q.name, "na")}
                  className="sr-only"
                />
                <span className={cn(
                  "w-4 h-4 rounded-full border flex items-center justify-center",
                  values[q.name] === "na" ? "border-gray-500 bg-gray-500" : "border-gray-300 dark:border-gray-600"
                )}>
                  {values[q.name] === "na" && <span className="w-2 h-2 rounded-full bg-white"></span>}
                </span>
                <span>N/A</span>
              </label>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}