"use client";

import { useEffect, useState } from "react";
import { useDashboardContext } from "@/components/dashboard/DashboardProvider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>hart, Pie, Cell, ResponsiveContainer, BarChart, Bar, XAxis, YAxis, Tooltip, Legend, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar, CartesianGrid, ReferenceLine, LineChart, Line, AreaChart, Area, Label } from "recharts";
import { Skeleton } from "@/components/ui/skeleton";
import { PieChartIcon, BarChart3, Activity, RadarIcon } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

export function AssessmentChartCard() {
  const { assessments, isLoading, committees, federation, score, calculateScore } = useDashboardContext();
  const { toast } = useToast();
  const [chartType, setChartType] = useState<'bar' | 'radar' | 'line'>('bar');

  // Calculate scores when component mounts
  useEffect(() => {
    console.log('AssessmentChartCard mounted, calculating scores...');
    calculateScore();
  }, [calculateScore]);

  // Define section names for the chart
  const sectionNames: Record<number, string> = {
    1: "Federation Info",
    2: "Legal Status",
    3: "Leadership",
    4: "Structure",
    5: "Planning",
    6: "Program Management",
    7: "Human Resources",
    8: "External Relations",
    9: "Member Engagement",
    10: "Financial Stability",
    11: "Audit & Compliance"
  };

  // Prepare data for the charts
  const getAssessmentData = () => {
    // Log assessments to debug
    console.log('Assessments data:', assessments);

    // Create a map of section numbers to section data
    const sectionMap = new Map();

    // Add all sections from 3 to 11 to ensure we have all sections represented
    // We start from 3 because section 1 and 2 doesn't have a score
    for (let i = 3; i <= 11; i++) {
      sectionMap.set(i, {
        name: sectionNames[i] || `Section ${i}`,
        score: 0,
        fullMark: 10,
        sectionNumber: i
      });
    }

    // Filter assessments with section numbers and update the map with actual scores
    assessments
      .filter(assessment => assessment.section_number && assessment.section_number >= 2)
      .forEach((assessment) => {
        const sectionNumber = assessment.section_number || 0;
        const sectionName = sectionNames[sectionNumber] || `Section ${sectionNumber}`;
        const score = typeof assessment.score === 'number' ? assessment.score : 0;

        // Only update if we have a valid section number
        if (sectionNumber >= 3) {
          sectionMap.set(sectionNumber, {
            name: sectionName,
            score: score,
            fullMark: 10,
            sectionNumber: sectionNumber
          });
        }
      });

    // Convert map to array and sort by section number
    const result = Array.from(sectionMap.values())
      .sort((a, b) => a.sectionNumber - b.sectionNumber);

    console.log('Processed assessment data:', result);

    return result;
  };

  // Prepare gender distribution data for pie chart
  const getGenderData = () => {
    let maleCount = 0;
    let femaleCount = 0;

    // Sum up male and female members from all committees
    committees.forEach(committee => {
      maleCount += committee.male_members || 0;
      femaleCount += committee.female_members || 0;
    });

    return [
      { name: 'Male', value: maleCount, color: '#3b82f6' },
      { name: 'Female', value: femaleCount, color: '#ec4899' },
    ];
  };

  if (isLoading) {
    return (
      <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full">
        <CardHeader className="pb-2">
          <Skeleton className="h-6 w-[180px] mb-2" />
          <Skeleton className="h-4 w-[250px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <Skeleton className="h-64 w-full rounded-lg" />
            <Skeleton className="h-64 w-full rounded-lg" />
          </div>
        </CardContent>
      </Card>
    );
  }

  const assessmentData = getAssessmentData();
  const genderData = getGenderData();

  // Check if we have any assessment data with scores
  const hasAssessmentData = assessmentData.length > 0;
  const hasScores = assessmentData.some(item => item.score > 0);

  // Always use the assessment data, even if scores are 0
  // This will show all sections with their proper names
  const displayData = assessmentData;

  // Log the data we're using for the chart
  console.log('Display data for chart:', displayData, 'Has scores:', hasScores);
  const hasGenderData = genderData.some(item => item.value > 0);

  // Custom tooltip for bar chart
  const CustomBarTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      // Get the section name from the payload for vertical layout
      const sectionName = payload[0].payload.name;
      const score = payload[0].value;
      const percentage = Math.round((score / 10) * 100);

      // Determine rating based on score
      let rating = '';
      let ratingColor = '';
      if (score >= 8) {
        rating = 'Excellent';
        ratingColor = '#10b981';
      } else if (score >= 6) {
        rating = 'Good';
        ratingColor = '#f59e0b';
      } else if (score >= 4) {
        rating = 'Fair';
        ratingColor = '#f59e0b';
      } else {
        rating = 'Needs Improvement';
        ratingColor = '#ef4444';
      }

      return (
        <div className="bg-background/95 backdrop-blur-sm border border-border p-4 rounded-lg shadow-md min-w-[180px]">
          <p className="font-semibold text-sm mb-1">{sectionName}</p>
          <div className="flex items-center gap-2 mb-1">
            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
              <div 
                className="h-full rounded-full transition-all duration-300" 
                style={{ 
                  width: `${percentage}%`, 
                  backgroundColor: ratingColor 
                }}
              />
            </div>
            <span className="font-medium text-sm whitespace-nowrap">{score}/10</span>
          </div>
          <div className="flex justify-between items-center mt-2">
            <p className="text-xs text-muted-foreground">{percentage}% complete</p>
            <span 
              className="text-xs px-2 py-1 rounded-full font-medium" 
              style={{ 
                backgroundColor: `${ratingColor}20`, 
                color: ratingColor 
              }}
            >
              {rating}
            </span>
          </div>
        </div>
      );
    }
    return null;
  };

  // Custom tooltip for pie chart
  const CustomPieTooltip = ({ active, payload }: any) => {
    if (active && payload && payload.length) {
      const totalMembers = genderData.reduce((sum, item) => sum + item.value, 0);
      const percentage = totalMembers > 0 ? (payload[0].value / totalMembers) * 100 : 0;
      const isLowRepresentation = percentage < 30 && payload[0].name === 'Female';
      
      return (
        <div className="bg-background/95 backdrop-blur-sm border border-border p-4 rounded-lg shadow-md min-w-[200px]">
          <div className="flex items-center gap-2 mb-1">
            <div 
              className="w-3 h-3 rounded-full" 
              style={{ backgroundColor: payload[0].color || payload[0].fill }}
            />
            <p className="font-semibold text-sm">{payload[0].name} Members</p>
          </div>
          
          <div className="mt-2 space-y-1">
            <div className="flex justify-between items-center">
              <span className="text-xs text-muted-foreground">Count:</span>
              <span className="font-bold">{payload[0].value}</span>
            </div>
            
            <div className="flex justify-between items-center">
              <span className="text-xs text-muted-foreground">Percentage:</span>
              <span className="font-medium">{percentage.toFixed(1)}%</span>
            </div>
            
            {isLowRepresentation && (
              <div className="mt-2 pt-2 border-t border-muted/50">
                <span className="text-xs px-2 py-0.5 bg-amber-100 dark:bg-amber-950 text-amber-800 dark:text-amber-300 rounded-full block text-center">
                  Low female representation
                </span>
              </div>
            )}
          </div>
        </div>
      );
    }
    return null;
  };

  return (
    <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
      <CardHeader className="pb-2 bg-muted/10">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <BarChart3 className="h-5 w-5 text-primary" />
              Assessment Visualization
            </CardTitle>
            <CardDescription>Visual representation of your assessment data</CardDescription>
          </div>
          {hasAssessmentData && (
            <div className="flex flex-wrap gap-2">
            </div>
          )}
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        {hasScores ? (
          <div className="space-y-8">
            <div>
              <div className="flex justify-between items-center mb-3">
                <h3 className="text-sm font-medium flex items-center gap-2">
                  <BarChart3 className="h-4 w-4 text-primary" />
                  Section Scores
                </h3>
                <Tabs defaultValue="bar" onValueChange={(value) => setChartType(value as 'bar' | 'radar' | 'line')}>
                  <TabsList className="grid grid-cols-3 h-8 w-auto">
                    <TabsTrigger value="bar" className="text-xs px-2 py-0 h-7">
                      <BarChart3 className="h-3 w-3 mr-1" />
                      Bar
                    </TabsTrigger>
                    <TabsTrigger value="radar" className="text-xs px-2 py-0 h-7">
                      <RadarIcon className="h-3 w-3 mr-1" />
                      Radar
                    </TabsTrigger>
                    <TabsTrigger value="line" className="text-xs px-2 py-0 h-7">
                      <Activity className="h-3 w-3 mr-1" />
                      Line
                    </TabsTrigger>
                  </TabsList>
                </Tabs>
              </div>
              <div className="h-[28rem] bg-muted/10 p-4 rounded-lg border border-muted/50">
                <ResponsiveContainer width="100%" height="100%">
                  {chartType === 'bar' && (
                    <BarChart
                      data={displayData}
                      margin={{ top: 20, right: 30, left: 30, bottom: 80 }}
                      barSize={30}
                      layout="vertical"
                      animationDuration={1200}
                      animationEasing="ease-in-out"
                    >
                      <defs>
                        {/* Define gradients for different score ranges */}
                        <linearGradient id="scoreGradientHigh" x1="0" y1="0" x2="1" y2="0">
                          <stop offset="0%" stopColor="#10b981" stopOpacity={0.8} />
                          <stop offset="100%" stopColor="#10b981" stopOpacity={1} />
                        </linearGradient>
                        <linearGradient id="scoreGradientMedium" x1="0" y1="0" x2="1" y2="0">
                          <stop offset="0%" stopColor="#f59e0b" stopOpacity={0.8} />
                          <stop offset="100%" stopColor="#f59e0b" stopOpacity={1} />
                        </linearGradient>
                        <linearGradient id="scoreGradientLow" x1="0" y1="0" x2="1" y2="0">
                          <stop offset="0%" stopColor="#ef4444" stopOpacity={0.8} />
                          <stop offset="100%" stopColor="#ef4444" stopOpacity={1} />
                        </linearGradient>
                      </defs>
                      <XAxis
                        type="number"
                        tick={{ fontSize: 12 }}
                        tickLine={false}
                        axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                        domain={[0, 10]}
                        label={{ 
                          value: 'Score (out of 10)', 
                          position: 'bottom', 
                          offset: 50,
                          style: { textAnchor: 'middle', fill: '#6b7280', fontSize: 14 }
                        }}
                        ticks={[0, 2, 4, 6, 8, 10]}
                      />
                      <YAxis
                        dataKey="name"
                        type="category"
                        tick={{ fontSize: 12, fill: '#6b7280', padding: 5, textAnchor: 'end' }}
                        tickLine={false}
                        axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                        width={160}
                        label={{ 
                          value: 'Assessment Sections', 
                          angle: -90, 
                          position: 'insideLeft',
                          style: { textAnchor: 'middle', fill: '#6b7280', fontSize: 14 }
                        }}
                      />
                      <Tooltip 
                        content={<CustomBarTooltip />} 
                        animationDuration={200}
                        animationEasing="ease-out"
                        cursor={{ fill: 'rgba(0, 0, 0, 0.05)' }}
                      />
                      <CartesianGrid 
                        strokeDasharray="3 3" 
                        horizontal={true} 
                        vertical={true} 
                        opacity={0.2} 
                      />
                      {/* Reference lines for score thresholds with improved styling */}
                      <ReferenceLine
                        x={4}
                        stroke="#ef4444"
                        strokeDasharray="3 3"
                        opacity={0.6}
                        label={{ 
                          value: 'Needs Improvement', 
                          position: 'top', 
                          fill: '#ef4444', 
                          fontSize: 11,
                          fontWeight: 500
                        }}
                      />
                      <ReferenceLine
                        x={6}
                        stroke="#f59e0b"
                        strokeDasharray="3 3"
                        opacity={0.6}
                        label={{ 
                          value: 'Fair', 
                          position: 'top', 
                          fill: '#f59e0b', 
                          fontSize: 11,
                          fontWeight: 500
                        }}
                      />
                      <ReferenceLine
                        x={8}
                        stroke="#10b981"
                        strokeDasharray="3 3"
                        opacity={0.6}
                        label={{ 
                          value: 'Good', 
                          position: 'top', 
                          fill: '#10b981', 
                          fontSize: 11,
                          fontWeight: 500
                        }}
                      />
                      <Bar
                        dataKey="score"
                        name="Score"
                        radius={[0, 6, 6, 0]}
                        animationDuration={1500}
                        animationBegin={300}
                        // Enhanced color fill with gradients based on score
                        fill="url(#scoreGradientMedium)"
                        isAnimationActive={true}
                      >
                        {displayData.map((entry, index) => {
                          const score = entry.score || 0;
                          let fillColor = 'url(#scoreGradientLow)';
                          if (score >= 8) fillColor = 'url(#scoreGradientHigh)';
                          else if (score >= 6) fillColor = 'url(#scoreGradientMedium)';
                          
                          return <Cell key={`cell-${index}`} fill={fillColor} filter="drop-shadow(0px 2px 2px rgba(0, 0, 0, 0.1))" />;
                        })}
                      </Bar>
                      <Legend 
                        verticalAlign="bottom" 
                        height={36} 
                        iconType="circle"
                        iconSize={10}
                        wrapperStyle={{ paddingTop: 20 }}
                        formatter={(value, entry) => {
                          return <span style={{ color: '#6b7280', fontSize: 12 }}>Section Scores</span>;
                        }}
                      />
                    </BarChart>
                  )}
                  
                  {chartType === 'radar' && (
                    <RadarChart 
                      cx="50%" 
                      cy="50%" 
                      outerRadius="70%" 
                      data={displayData}
                      margin={{ top: 20, right: 30, left: 30, bottom: 20 }}
                    >
                      <PolarGrid stroke="#e5e7eb" strokeDasharray="3 3" />
                      <PolarAngleAxis 
                        dataKey="name" 
                        tick={{ fontSize: 11, fill: '#6b7280' }}
                        tickLine={false}
                        axisLine={{ strokeWidth: 1, stroke: '#e5e7eb' }}
                      />
                      <PolarRadiusAxis 
                        angle={90} 
                        domain={[0, 10]} 
                        tick={{ fontSize: 10, fill: '#6b7280' }}
                        tickCount={6}
                        stroke="#e5e7eb"
                        axisLine={{ strokeWidth: 1, stroke: '#e5e7eb' }}
                      />
                      <Tooltip 
                        content={<CustomBarTooltip />}
                        animationDuration={200}
                        animationEasing="ease-out"
                      />
                      <Radar 
                        name="Score" 
                        dataKey="score" 
                        stroke="#3b82f6" 
                        fill="#3b82f6" 
                        fillOpacity={0.5}
                        animationDuration={1500}
                        animationBegin={300}
                      />
                      <Legend 
                        verticalAlign="bottom" 
                        height={36} 
                        iconType="circle"
                        iconSize={10}
                        wrapperStyle={{ paddingTop: 20 }}
                        formatter={(value, entry) => {
                          return <span style={{ color: '#6b7280', fontSize: 12 }}>Section Scores</span>;
                        }}
                      />
                    </RadarChart>
                  )}
                  
                  {chartType === 'line' && (
                    <AreaChart
                      data={displayData}
                      margin={{ top: 20, right: 120, left: 30, bottom: 80 }}
                      animationDuration={1200}
                      animationEasing="ease-in-out"
                    >
                      <defs>
                        <linearGradient id="scoreGradientArea" x1="0" y1="0" x2="0" y2="1">
                          <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.8} />
                          <stop offset="100%" stopColor="#3b82f6" stopOpacity={0.1} />
                        </linearGradient>
                      </defs>
                      <XAxis
                        dataKey="name"
                        tick={{ fontSize: 12, fill: '#6b7280' }}
                        tickLine={false}
                        axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                        angle={-45}
                        textAnchor="end"
                        height={80}
                        interval={0}
                      />
                      <YAxis
                        tick={{ fontSize: 12, fill: '#6b7280' }}
                        tickLine={false}
                        axisLine={{ stroke: '#e5e7eb', strokeWidth: 1 }}
                        domain={[0, 10]}
                        label={{ 
                          value: 'Score (out of 10)', 
                          angle: -90, 
                          position: 'insideLeft',
                          style: { textAnchor: 'middle', fill: '#6b7280', fontSize: 14 }
                        }}
                        ticks={[0, 2, 4, 6, 8, 10]}
                      />
                      <Tooltip 
                        content={<CustomBarTooltip />}
                        animationDuration={200}
                        animationEasing="ease-out"
                      />
                      <CartesianGrid 
                        strokeDasharray="3 3" 
                        horizontal={true} 
                        vertical={true} 
                        opacity={0.2} 
                      />
                      <ReferenceLine
                        y={4}
                        stroke="#ef4444"
                        strokeDasharray="3 3"
                        opacity={0.6}
                        label={{ 
                          value: 'Needs Improvement', 
                          position: 'right', 
                          fill: '#ef4444', 
                          fontSize: 11,
                          fontWeight: 500
                        }}
                      />
                      <ReferenceLine
                        y={6}
                        stroke="#f59e0b"
                        strokeDasharray="3 3"
                        opacity={0.6}
                        label={{ 
                          value: 'Fair', 
                          position: 'right', 
                          fill: '#f59e0b', 
                          fontSize: 11,
                          fontWeight: 500
                        }}
                      />
                      <ReferenceLine
                        y={8}
                        stroke="#10b981"
                        strokeDasharray="3 3"
                        opacity={0.6}
                        label={{ 
                          value: 'Good', 
                          position: 'right', 
                          fill: '#10b981', 
                          fontSize: 11,
                          fontWeight: 500
                        }}
                      />
                      <Area 
                        type="monotone" 
                        dataKey="score" 
                        stroke="#3b82f6" 
                        strokeWidth={2}
                        fill="url(#scoreGradientArea)" 
                        activeDot={{ r: 6, strokeWidth: 0 }}
                        animationDuration={1500}
                        animationBegin={300}
                      />
                      <Line 
                        type="monotone" 
                        dataKey="score" 
                        stroke="#3b82f6" 
                        strokeWidth={3}
                        dot={{ stroke: '#3b82f6', strokeWidth: 2, r: 4, fill: 'white' }}
                        activeDot={{ r: 6, strokeWidth: 0, fill: '#3b82f6' }}
                        animationDuration={1500}
                        animationBegin={300}
                      />
                      <Legend 
                        verticalAlign="bottom" 
                        height={36} 
                        iconType="line"
                        iconSize={10}
                        wrapperStyle={{ paddingTop: 20 }}
                        formatter={(value, entry) => {
                          return <span style={{ color: '#6b7280', fontSize: 12 }}>Section Scores</span>;
                        }}
                      />
                    </AreaChart>
                  )}
                </ResponsiveContainer>
              </div>
            </div>

            {hasGenderData && (
              <div>
                <div className="flex justify-between items-center mb-3">
                  <h3 className="text-sm font-medium flex items-center gap-2">
                    <PieChartIcon className="h-4 w-4 text-primary" />
                    Committee Gender Distribution
                  </h3>
                </div>
                <div className="h-72 bg-muted/10 p-4 rounded-lg border border-muted/50">
                  <div className="grid grid-cols-1 md:grid-cols-3 h-full">
                    <div className="col-span-2 flex items-center justify-center">
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <defs>
                            <linearGradient id="maleGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#3b82f6" stopOpacity={0.9} />
                              <stop offset="100%" stopColor="#2563eb" stopOpacity={1} />
                            </linearGradient>
                            <linearGradient id="femaleGradient" x1="0" y1="0" x2="0" y2="1">
                              <stop offset="0%" stopColor="#ec4899" stopOpacity={0.9} />
                              <stop offset="100%" stopColor="#db2777" stopOpacity={1} />
                            </linearGradient>
                            <filter id="shadow" height="200%">
                              <feDropShadow dx="0" dy="3" stdDeviation="3" floodOpacity="0.1"/>
                            </filter>
                          </defs>
                          <Pie
                            data={genderData}
                            cx="50%"
                            cy="50%"
                            innerRadius={60}
                            outerRadius={100}
                            paddingAngle={2}
                            cornerRadius={6}
                            dataKey="value"
                            startAngle={90}
                            endAngle={-270}
                            animationDuration={1500}
                            animationBegin={300}
                            filter="url(#shadow)"
                          >
                            {genderData.map((entry, index) => {
                              const fillId = entry.name === 'Male' ? 'url(#maleGradient)' : 'url(#femaleGradient)';
                              return (
                                <Cell
                                  key={`cell-${index}`}
                                  fill={fillId}
                                  stroke="rgba(255,255,255,0.5)"
                                  strokeWidth={2}
                                />
                              );
                            })}
                            <Label
                              content={({ viewBox }) => {
                                const { cx, cy } = viewBox as { cx: number; cy: number };
                                const totalMembers = genderData.reduce((sum, entry) => sum + entry.value, 0);
                                return (
                                  <g>
                                    <text x={cx} y={cy} textAnchor="middle" dominantBaseline="middle" className="text-[34px] font-bold fill-foreground">
                                      {totalMembers}
                                    </text>
                                    <text x={cx} y={cy + 20} textAnchor="middle" dominantBaseline="middle" className="text-xs fill-muted-foreground">
                                      Total Members
                                    </text>
                                  </g>
                                );
                              }}
                            />
                          </Pie>
                          <Tooltip 
                            content={<CustomPieTooltip />} 
                            animationDuration={200}
                            animationEasing="ease-out"
                          />
                          <Legend 
                            verticalAlign="middle" 
                            align="right"
                            layout="vertical"
                            iconType="circle"
                            iconSize={10}
                            formatter={(value, entry, index) => {
                              const { payload } = entry as any;
                              const color = payload.name === 'Male' ? '#3b82f6' : '#ec4899';
                              return (
                                <span style={{ color: 'var(--foreground)', marginLeft: '5px' }}>
                                  {payload.name}
                                </span>
                              );
                            }}
                          />
                        </PieChart>
                      </ResponsiveContainer>
                    </div>
                    <div className="flex flex-col justify-center space-y-4 p-2">
                      {genderData.map((entry, index) => {
                        const totalMembers = genderData.reduce((sum, item) => sum + item.value, 0);
                        const percentage = totalMembers > 0 ? Math.round((entry.value / totalMembers) * 100) : 0;
                        const isLowRepresentation = percentage < 30;
                        
                        return (
                          <div key={`stat-${index}`} className="space-y-1">
                            <div className="flex justify-between items-center">
                              <div className="flex items-center gap-2">
                                <div 
                                  className="w-3 h-3 rounded-full" 
                                  style={{ backgroundColor: entry.color }}
                                />
                                <span className="text-sm font-medium">{entry.name}</span>
                              </div>
                              <span className="text-sm font-bold">{entry.value}</span>
                            </div>
                            <div className="h-2 w-full bg-muted rounded-full overflow-hidden">
                              <div 
                                className="h-full rounded-full transition-all duration-500" 
                                style={{ 
                                  width: `${percentage}%`, 
                                  backgroundColor: entry.color 
                                }}
                              />
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-xs text-muted-foreground">{percentage}%</span>
                              {isLowRepresentation && entry.name === 'Female' && (
                                <span className="text-xs px-2 py-0.5 bg-amber-100 dark:bg-amber-950 text-amber-800 dark:text-amber-300 rounded-full">
                                  Low representation
                                </span>
                              )}
                            </div>
                          </div>
                        );
                      })}
                      
                      <div className="pt-2 mt-2 border-t border-muted/50">
                        <div className="text-xs text-muted-foreground space-y-1">
                          <p className="font-medium">Gender Balance:</p>
                          {(() => {
                            const maleCount = genderData.find(d => d.name === 'Male')?.value || 0;
                            const femaleCount = genderData.find(d => d.name === 'Female')?.value || 0;
                            const totalMembers = maleCount + femaleCount;
                            const femalePercentage = totalMembers > 0 ? Math.round((femaleCount / totalMembers) * 100) : 0;
                            
                            if (femalePercentage >= 45 && femalePercentage <= 55) {
                              return (
                                <span className="text-xs px-2 py-0.5 bg-green-100 dark:bg-green-950 text-green-800 dark:text-green-300 rounded-full inline-block">
                                  Balanced (45-55%)
                                </span>
                              );
                            } else if (femalePercentage < 30) {
                              return (
                                <span className="text-xs px-2 py-0.5 bg-red-100 dark:bg-red-950 text-red-800 dark:text-red-300 rounded-full inline-block">
                                  Significant imbalance
                                </span>
                              );
                            } else {
                              return (
                                <span className="text-xs px-2 py-0.5 bg-amber-100 dark:bg-amber-950 text-amber-800 dark:text-amber-300 rounded-full inline-block">
                                  Moderate imbalance
                                </span>
                              );
                            }
                          })()} 
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-center bg-muted/10 rounded-lg border border-dashed border-muted p-6">
            <div className="bg-muted/30 p-4 rounded-full mb-3">
              <BarChart3 className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-base font-medium">No Assessment Scores Available</h3>
            <p className="text-sm text-muted-foreground mt-2 max-w-xs">
              {hasAssessmentData ?
                "Your assessment data is loaded but no scores have been calculated yet." :
                "No assessment data found. Please complete assessment sections first."}
            </p>
            <p className="text-xs text-muted-foreground mt-4 max-w-xs italic">
              Note: Make sure to answer all questions in each section to generate scores
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
