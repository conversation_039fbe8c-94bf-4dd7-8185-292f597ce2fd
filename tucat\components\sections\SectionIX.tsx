"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionIX() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    communication_effectiveness: "",
    member_engagement: "",
    participation_opportunities: "",
    feedback_collection: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 9
    const sectionData = assessments.find(a => a.section_number === 9);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        communication_effectiveness: sectionData.communication_effectiveness || "",
        member_engagement: sectionData.member_engagement || "",
        participation_opportunities: sectionData.participation_opportunities || "",
        feedback_collection: sectionData.feedback_collection || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(9, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  return (
    <QuestionCard
      title="MEMBER ENGAGEMENT AND COMMUNICATION"
      bengaliTitle="সদস্যদের সম্পৃক্ততা এবং যোগাযোগ"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the effectiveness of the federation's communication strategies and member engagement practices.",
        bengali: "ফেডারেশনের যোগাযোগ কৌশল এবং সদস্য সম্পৃক্ততার অনুশীলনের কার্যকারিতা মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="How effectively does the federation communicate with its members?"
        bengaliQuestion="ফেডারেশন কীভাবে তার সদস্যদের সাথে যোগাযোগ করে?"
        value={formData.communication_effectiveness}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, communication_effectiveness: value }))
        }
        options={[
          {
            value: "1",
            label: "Not effective - There is little to no regular communication with members",
            bengaliLabel: "কার্যকর নয় - সদস্যদের সাথে নিয়মিত যোগাযোগ খুবই কম বা একেবারেই নেই।"
          },
          {
            value: "2",
            label: "Somewhat effective - Communication is sporadic and does not reach all members",
            bengaliLabel: "কিছুটা কার্যকর - যোগাযোগ মাঝে মাঝে হয় এবং সব সদস্যদের কাছে পৌঁছায় না।"
          },
          {
            value: "3",
            label: "Moderately effective - Communication is good but could improve in consistency",
            bengaliLabel: "মাঝামাঝি কার্যকর - যোগাযোগ ভালো, তবে ধারাবাহিকতা আরও বাড়ানো যেতে পারে।"
          },
          {
            value: "4",
            label: "Very effective - Communication is regular, transparent, and reaches all members",
            bengaliLabel: "খুব কার্যকর - যোগাযোগ নিয়মিত, স্বচ্ছ এবং সকল সদস্যদের সাথে যোগাযোগ করা হয়।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={2}
        question="How engaged are members in federation activities and decision-making?"
        bengaliQuestion="ফেডারেশনের কার্যক্রম এবং সিদ্ধান্ত গ্রহণে সদস্যরা কতটা সম্পৃক্ত?"
        value={formData.member_engagement}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, member_engagement: value }))
        }
        options={[
          {
            value: "1",
            label: "Not engaged - Most members are not involved in federation activities or decisions",
            bengaliLabel: "সম্পৃক্ত নয় - বেশিরভাগ সদস্য ফেডারেশনের কার্যক্রম বা সিদ্ধান্ত গ্রহণে অংশ নেয় না।"
          },
          {
            value: "2",
            label: "Somewhat engaged - Engagement is limited to a small group of members",
            bengaliLabel: "কিছুটা সম্পৃক্ত - সদস্যদের ছোট একটি দলই সীমিতভাবে সম্পৃক্ত।"
          },
          {
            value: "3",
            label: "Moderately engaged - Some members participate, but engagement could be stronger",
            bengaliLabel: "মাঝামাঝি সম্পৃক্ত - কিছু সদস্য অংশ নেয়, তবে সম্পৃক্ততা আরও জোরদার হতে পারে।"
          },
          {
            value: "4",
            label: "Highly engaged - Members are actively involved in activities and decisions",
            bengaliLabel: "অত্যন্ত সম্পৃক্ত - সদস্যরা সক্রিয়ভাবে কার্যক্রম এবং সিদ্ধান্ত গ্রহণে অংশগ্রহণ করে।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={3}
        question="How well does the federation provide opportunities for members to express concerns and contribute to decision-making?"
        bengaliQuestion="ফেডারেশন কীভাবে সদস্যদের উদ্বেগ প্রকাশের এবং সিদ্ধান্ত গ্রহণে অংশগ্রহণের সুযোগ করে দেয়?"
        value={formData.participation_opportunities}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, participation_opportunities: value }))
        }
        options={[
          {
            value: "1",
            label: "Not well - There are no clear opportunities for member participation",
            bengaliLabel: "ভালো নয় - সদস্যদের অংশগ্রহণের জন্য কোনো সুযোগ নেই।"
          },
          {
            value: "2",
            label: "Somewhat well - Opportunities are limited",
            bengaliLabel: "কিছুটা ভালো - সুযোগ রয়েছে কিন্তু সীমিত।"
          },
          {
            value: "3",
            label: "Moderately well - Opportunities exist, but are underutilized",
            bengaliLabel: "মাঝামাঝি ভালো - সুযোগ বিদ্যমান, তবে পর্যাপ্ত সম্পূর্ণভাবে ব্যবহার করা হয় না।"
          },
          {
            value: "4",
            label: "Very well - There are clear opportunities for member participation",
            bengaliLabel: "খুব ভালো - সদস্যদের উদ্বেগ প্রকাশের ও সিদ্ধান্ত গ্রহনে অংশগ্রহণের জন্য সুস্পষ্ট সুযোগ রয়েছে।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={4}
        question="Does the federation collect feedback from members on the services and support they receive?"
        bengaliQuestion="ফেডারেশন কি সদস্যদের কাছ থেকে তাদের প্রাপ্ত সেবা ও সহায়তা সম্পর্কে মতামত সংগ্রহ করে?"
        value={formData.feedback_collection}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, feedback_collection: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, feedback is regularly collected and acted upon",
            bengaliLabel: "হ্যাঁ, নিয়মিতভাবে মতামত সংগ্রহ করা হয় এবং তা কার্যকর হয়।"
          },
          {
            value: "b",
            label: "Somewhat – Feedback is collected but not always acted upon",
            bengaliLabel: "কিছুটা - মতামত সংগ্রহ করা হয় কিন্তু তা সবসময় কার্যকর হয় না।"
          },
          {
            value: "c",
            label: "No, feedback is rarely collected or acted upon",
            bengaliLabel: "না, মতামত খুব কমই সংগ্রহ করা হয় বা কার্যকর করা হয়।"
          }
        ]}
      />
    </QuestionCard>
  );
}