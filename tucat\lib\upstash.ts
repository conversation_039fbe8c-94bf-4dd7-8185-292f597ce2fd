import { Redis } from '@upstash/redis';

export const redis = new Redis({
  url: process.env.NEXT_PUBLIC_UPSTASH_REDIS_URL!,
  token: process.env.NEXT_PUBLIC_UPSTASH_REDIS_TOKEN!,
});

// Helper function to handle Redis operations with error checking
export async function safeRedisOperation<T>(operation: () => Promise<T>): Promise<T> {
  try {
    const result = await operation();
    if (result === null) {
      return {} as T;
    }
    return result;
  } catch (error: any) {
    if (error.message?.includes('NOPERM')) {
      console.error('Redis authentication failed:', error);
      throw new Error('Database access denied. Please check your credentials.');
    }
    if (error.message?.includes('connect')) {
      console.error('Redis connection error:', error);
      throw new Error('Failed to connect to database. Please try again.');
    }
    if (error.message?.includes('JSON')) {
      console.error('Redis JSON parsing error:', error);
      throw new Error('Error retrieving user data. Invalid data format.');
    }
    console.error('Redis operation error:', error);
    throw new Error('Error retrieving user data. Please try again.');
  }
}