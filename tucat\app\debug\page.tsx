"use client";

import { useEffect, useState } from 'react';

export default function DebugPage() {
  const [info, setInfo] = useState({
    windowWidth: 0,
    windowHeight: 0,
    userAgent: '',
    cssLoaded: false,
    jsLoaded: true,
    mounted: false
  });

  useEffect(() => {
    // Check if CSS is loaded
    const checkCssLoaded = () => {
      const testElement = document.createElement('div');
      testElement.className = 'hidden';
      document.body.appendChild(testElement);
      const isHidden = window.getComputedStyle(testElement).display === 'none';
      document.body.removeChild(testElement);
      return isHidden;
    };

    setInfo({
      windowWidth: window.innerWidth,
      windowHeight: window.innerHeight,
      userAgent: navigator.userAgent,
      cssLoaded: checkCssLoaded(),
      jsLoaded: true,
      mounted: true
    });

    const handleResize = () => {
      setInfo(prev => ({
        ...prev,
        windowWidth: window.innerWidth,
        windowHeight: window.innerHeight
      }));
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  if (!info.mounted) {
    return <div>Loading debug information...</div>;
  }

  return (
    <div className="p-8 max-w-4xl mx-auto">
      <h1 className="text-3xl font-bold mb-6">Debug Information</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Browser Information</h2>
          <ul className="space-y-2">
            <li><strong>Window Width:</strong> {info.windowWidth}px</li>
            <li><strong>Window Height:</strong> {info.windowHeight}px</li>
            <li><strong>User Agent:</strong> {info.userAgent}</li>
          </ul>
        </div>
        
        <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Resource Status</h2>
          <ul className="space-y-2">
            <li>
              <strong>CSS Loaded:</strong>{' '}
              <span className={info.cssLoaded ? "text-green-500" : "text-red-500"}>
                {info.cssLoaded ? "Yes" : "No"}
              </span>
            </li>
            <li>
              <strong>JavaScript Loaded:</strong>{' '}
              <span className={info.jsLoaded ? "text-green-500" : "text-red-500"}>
                {info.jsLoaded ? "Yes" : "No"}
              </span>
            </li>
            <li>
              <strong>Component Mounted:</strong>{' '}
              <span className="text-green-500">Yes</span>
            </li>
          </ul>
        </div>
      </div>
      
      <div className="mt-8 bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md">
        <h2 className="text-xl font-semibold mb-4">Visual Test</h2>
        <div className="grid grid-cols-2 gap-4">
          <div className="bg-blue-500 text-white p-4 rounded">Blue Box</div>
          <div className="bg-red-500 text-white p-4 rounded">Red Box</div>
          <div className="bg-green-500 text-white p-4 rounded">Green Box</div>
          <div className="bg-yellow-500 text-white p-4 rounded">Yellow Box</div>
        </div>
      </div>
      
      <div className="mt-8 text-center">
        <a href="/login" className="bg-primary text-white px-6 py-2 rounded-md hover:bg-primary/80 transition-colors">
          Return to Login Page
        </a>
      </div>
    </div>
  );
}