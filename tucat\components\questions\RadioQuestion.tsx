"use client";

import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { cn } from "@/lib/utils";
import { HelpCircle } from "lucide-react";
import { useState } from "react";
import {
  Too<PERSON><PERSON>,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";

interface Option {
  value: string;
  label: string;
  bengaliLabel: string;
}

interface RadioQuestionProps {
  questionNumber: number;
  question: string;
  bengaliQuestion: string;
  options: Option[];
  value: string;
  onChange: (value: string) => void;
  helpText?: string;
}

export function RadioQuestion({
  questionNumber,
  question,
  bengaliQuestion,
  options,
  value,
  onChange,
  helpText,
}: RadioQuestionProps) {
  const [selectedOption, setSelectedOption] = useState(value);
  const handleChange = (newValue: string) => {
    setSelectedOption(newValue);
    onChange(newValue);
  };

  return (
    <div className="space-y-4 p-4 rounded-lg border border-border/50 hover:border-border transition-all duration-200 bg-card/50">
      <div className="flex items-start gap-2">
        <div className="bg-primary/10 text-primary font-medium rounded-full w-6 h-6 flex items-center justify-center flex-shrink-0 mt-0.5">
          {questionNumber}
        </div>
        <div className="flex-1">
          <div className="flex items-start justify-between">
            <Label className="text-base font-medium">
              {question}
              <span className="block text-sm text-muted-foreground mt-1">
                {bengaliQuestion}
              </span>
            </Label>
            {helpText && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <button className="ml-2 text-muted-foreground hover:text-foreground transition-colors">
                      <HelpCircle className="h-4 w-4" />
                    </button>
                  </TooltipTrigger>
                  <TooltipContent className="max-w-xs">
                    <p className="text-sm">{helpText}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </div>
        </div>
      </div>
      <RadioGroup
        className="space-y-3 pl-8"
        value={value}
        onValueChange={handleChange}
      >
        {options.map((option, index) => (
          <div
            key={option.value}
            className={cn(
              "flex items-start space-x-2 p-3 rounded-md border transition-all duration-200",
              value === option.value ?
                "border-primary/50 bg-primary/5 shadow-sm" :
                "border-transparent hover:border-border/50 hover:bg-muted/30"
            )}
          >
            <RadioGroupItem
              value={option.value}
              id={`q${questionNumber}-${option.value}`}
              className="mt-1"
            />
            <Label
              htmlFor={`q${questionNumber}-${option.value}`}
              className="leading-tight cursor-pointer"
            >
              {option.label}
              <span className="block text-sm text-muted-foreground">
                {option.bengaliLabel}
              </span>
            </Label>
          </div>
        ))}
      </RadioGroup>
    </div>
  );
}