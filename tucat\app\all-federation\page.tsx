"use client";

import { useState, useEffect } from 'react';
import { DashboardProvider } from "@/components/dashboard/DashboardProvider";
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { UserNav } from "@/components/UserNav";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Building2, UserCircle } from "lucide-react";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Federation } from "@/lib/types";
import { useAuth } from "@/components/AuthContext";
import FederationAnalyticsChart from "@/components/dashboard/FederationAnalyticsChart";
import SectionAverageChart from "@/components/dashboard/SectionAverageChart";
import YouthRepresentationChart from "@/components/dashboard/YouthRepresentationChart";

export default function AllFederationPage() {
  const { user } = useAuth();
  const [federations, setFederations] = useState<Partial<Federation>[]>([]);
  const [selectedFederation, setSelectedFederation] = useState<string>("all");
  const [isLoading, setIsLoading] = useState(true);

  // Calculated averages
  const [averagePresidentAge, setAveragePresidentAge] = useState<number>(0);
  const [averageSecretaryAge, setAverageSecretaryAge] = useState<number>(0);
  // Score analytics
  const [averageTotalScore, setAverageTotalScore] = useState<number>(0);
  const [averagePercentage, setAveragePercentage] = useState<number>(0);
  const [highestScore, setHighestScore] = useState<number>(0);
  const [lowestScore, setLowestScore] = useState<number>(0);
  const [highestPercentage, setHighestPercentage] = useState<number>(0);
  const [lowestPercentage, setLowestPercentage] = useState<number>(0);
  // Youth and section analytics
  const [averageYouthPercentage, setAverageYouthPercentage] = useState<number>(0);
  const [avgSectionScores, setAvgSectionScores] = useState<{ [section: number]: number }>({});
  const [minSectionScores, setMinSectionScores] = useState<{ [section: number]: number }>({});
  const [maxSectionScores, setMaxSectionScores] = useState<{ [section: number]: number }>({});
  const [sectionList, setSectionList] = useState<{ id: number; title: string }[]>([]);
  // Selected federation details
  const [currentFederation, setCurrentFederation] = useState<Partial<Federation> | null>(null);
  const [currentSectionScores, setCurrentSectionScores] = useState<{ [section: number]: number }>({});

  // Section titles (should match backend section numbers 3-11)
  // Section max scores (from useAssessment.ts)
  const sectionMaxScores: { [section: number]: number } = {
    3: 14,
    4: 4,
    5: 11,
    6: 9,
    7: 7,
    8: 8,
    9: 11,
    10: 16,
    11: 10,
  };
  useEffect(() => {
    setSectionList([
      { id: 3, title: "Leadership" },
      { id: 4, title: "Organizational Structure" },
      { id: 5, title: "Management" },
      { id: 6, title: "Worker Participation" },
      { id: 7, title: "Culture and Gender" },
      { id: 8, title: "Collective Bargaining" },
      { id: 9, title: "Member Engagement" },
      { id: 10, title: "Financial Stability" },
      { id: 11, title: "Audit & Compliance" },
    ]);
  }, []);
  // Fetch all federations
  useEffect(() => {
    const fetchFederations = async () => {
      try {
        // This would be replaced with your actual API endpoint
        const response = await fetch('/api/federations');
        if (!response.ok) throw new Error('Failed to fetch federations');

        const data = await response.json();
        setFederations(data.federations || []);
        // Calculate averages
        if ((data.federations || []).length > 0) {
          const presidentAges = data.federations.filter((fed: Partial<Federation>) => fed.president_age).map((fed: Partial<Federation>) => fed.president_age as number);
          const secretaryAges = data.federations.filter((fed: Partial<Federation>) => fed.secretary_age).map((fed: Partial<Federation>) => fed.secretary_age as number);
          const avgPresidentAge = presidentAges.length > 0
            ? Math.round(presidentAges.reduce((sum: number, age: number) => sum + age, 0) / presidentAges.length)
            : 0;
          const avgSecretaryAge = secretaryAges.length > 0
            ? Math.round(secretaryAges.reduce((sum: number, age: number) => sum + age, 0) / secretaryAges.length)
            : 0;
          setAveragePresidentAge(avgPresidentAge);
          setAverageSecretaryAge(avgSecretaryAge);
        }
        // Set score analytics
        setAverageTotalScore(data.avgTotalScore || 0);
        setAveragePercentage(data.avgPercentage || 0);
        setHighestScore(data.highestScore || 0);
        setLowestScore(data.lowestScore || 0);
        setHighestPercentage(data.highestPercentage || 0);
        setLowestPercentage(data.lowestPercentage || 0);
        setAverageYouthPercentage(data.avgYouthPercentage || 0);
        setAvgSectionScores(data.avgSectionScores || {});
        setMinSectionScores(data.minSectionScores || {});
        setMaxSectionScores(data.maxSectionScores || {});
      } catch (error) {
        console.error('Error fetching federations:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchFederations();
  }, []);

  // Handle federation selection change
  const handleFederationChange = (value: string) => {
    setSelectedFederation(value);

    if (value === 'all') {
      setCurrentFederation(null);
      setCurrentSectionScores({});
    } else {
      const selected = federations.find(fed => fed.id === value);
      setCurrentFederation(selected || null);
      setCurrentSectionScores(selected?.sectionScores || {});
    }
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen flex-col bg-background/50">
        <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="w-full px-4 md:px-6 flex h-16 items-center py-4">
            <div className="flex items-center gap-3">
              <Link href="/dashboard">
                <Button variant="ghost" size="icon" className="rounded-full">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex flex-col">
                <h1 className="text-xl font-bold">All Federation</h1>
                <p className="text-sm text-muted-foreground">
                  {selectedFederation === 'all' ? 'Viewing all federations' : currentFederation?.name}
                </p>
              </div>
            </div>
            <div className="ml-auto flex items-center gap-2 sm:gap-4">
              <ThemeToggle />
              <UserNav />
            </div>
          </div>
        </header>

        <main className="flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto">
          {/* New Title and Logos Section */}
          <div className="mb-8 text-center">
            <h1 className="text-2xl font-bold mb-4">Support for Effective and Inclusive Trade Unions in Bangladesh</h1>
            <div className="mt-4 flex justify-center gap-8">
              <img src="https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png" alt="German Cooperation Bangladesh" className="h-16 w-auto" />
              <img src="https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png" alt="International Labour Organization" className="h-16 w-auto" />
              <img src="https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png" alt="Growing Together OPC" className="h-16 w-auto" />
              <img src="https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png" alt="Government of the people's republic of Bangladesh" className="h-16 w-auto" />
            </div>
          </div>
          {/* --- Federation Analytics Chart --- */}
          <div className="mb-8">
            <FederationAnalyticsChart
              federations={federations}
              selectedFederation={selectedFederation}
              onSelectFederation={handleFederationChange}
            />
          </div>
          {/* --- Section-wise Average Chart --- */}
          <div className="mb-8">
            <SectionAverageChart
              avgSectionScores={avgSectionScores}
              minSectionScores={minSectionScores}
              maxSectionScores={maxSectionScores}
              sectionList={sectionList}
              sectionMaxScores={sectionMaxScores}
            />
          </div>
          {/* --- Youth Representation Chart --- */}
          <div className="mb-8">
            <YouthRepresentationChart federations={federations} />
          </div>

          {isLoading ? (
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="shadow-md animate-pulse">
                <CardHeader>
                  <CardTitle>Loading...</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="h-20 bg-muted rounded"></div>
                </CardContent>
              </Card>
            </div>
          ) : (
            <div className="grid gap-6 md:grid-cols-2">
              <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
                <CardHeader className="pb-2 bg-muted/10">
                  <CardTitle className="text-xl flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-primary" />
                    {selectedFederation === 'all' ? 'All Federations' : 'Federation Details'}
                  </CardTitle>
                  <CardDescription>
                    {selectedFederation === 'all'
                      ? 'Average data across all federations'
                      : `Details for ${currentFederation?.name}`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-6">
                    <div className="flex items-start space-x-4">
                      <div className="bg-blue-500/10 p-2.5 rounded-full">
                        <UserCircle className="h-5 w-5 text-blue-500" />
                      </div>
                      <div className="flex-1">
                        <h3 className="font-medium text-sm text-muted-foreground">Leadership</h3>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-2 mt-1">
                          <div className="bg-muted/30 p-2 rounded-md">
                            <p className="text-xs text-muted-foreground">President</p>
                            <p className="font-medium">
                              {selectedFederation === 'all'
                                ? 'Average Age'
                                : currentFederation?.president_name || 'Not specified'}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({selectedFederation === 'all'
                                  ? `${averagePresidentAge} years`
                                  : currentFederation?.president_age
                                    ? `${currentFederation.president_age} years`
                                    : 'Unknown'})
                              </span>
                            </p>
                          </div>
                          <div className="bg-muted/30 p-2 rounded-md">
                            <p className="text-xs text-muted-foreground">Secretary</p>
                            <p className="font-medium">
                              {selectedFederation === 'all'
                                ? 'Average Age'
                                : currentFederation?.secretary_name || 'Not specified'}
                              <span className="text-xs text-muted-foreground ml-1">
                                ({selectedFederation === 'all'
                                  ? `${averageSecretaryAge} years`
                                  : currentFederation?.secretary_age
                                    ? `${currentFederation.secretary_age} years`
                                    : 'Unknown'})
                              </span>
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>

              <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
                <CardHeader className="pb-2 bg-muted/10">
                  <CardTitle className="text-xl flex items-center gap-2">
                    <Building2 className="h-5 w-5 text-primary" />
                    Federation Statistics
                  </CardTitle>
                  <CardDescription>
                    {selectedFederation === 'all'
                      ? `Data from all ${federations.length} federations`
                      : `Statistics for ${currentFederation?.name}`}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-6">
                  <div className="space-y-4">
                    <p className="text-sm">
                      <span className="font-medium">Total Federations:</span> {federations.length}
                    </p>
                    {selectedFederation === 'all' ? (
                      <div className="p-4 bg-muted/20 rounded-md space-y-2">
                        <h4 className="font-medium mb-2">Average Scores</h4>
                        <p className="text-sm">Total Score: {averageTotalScore}</p>
                        <p className="text-sm">Percentage: {averagePercentage}%</p>
                        <p className="text-sm">Avg Youth Percentage: {averageYouthPercentage}%</p>
                        <h4 className="font-medium mt-4 mb-2">Section by Section Avg Score</h4>
                        <ul className="text-sm pl-4">
                          {sectionList.map(section => (
                            <li key={section.id} className="mb-1 flex justify-between">
                              <span>{section.title}</span>
                              <span>{avgSectionScores[section.id] !== undefined ? `${avgSectionScores[section.id].toFixed(2)} out of ${sectionMaxScores[section.id]}` : `N/A out of ${sectionMaxScores[section.id]}`}</span>
                            </li>
                          ))}
                        </ul>
                        <h4 className="font-medium mt-4 mb-2">Score Range</h4>
                        <p className="text-sm">Highest Score: {highestScore} ({highestPercentage}%)</p>
                        <p className="text-sm">Lowest Score: {lowestScore} ({lowestPercentage}%)</p>
                      </div>
                    ) : currentFederation ? (
                      <div className="p-4 bg-muted/20 rounded-md space-y-2">
                        <h4 className="font-medium mb-2">Federation Score</h4>
                        <p className="text-sm">Total Score: {currentFederation.totalScore ?? 'N/A'}</p>
                        <p className="text-sm">Max Score: {currentFederation.maxScore ?? 'N/A'}</p>
                        <p className="text-sm">Percentage: {currentFederation.percentage ?? 'N/A'}%</p>
                        <p className="text-sm">Youth Percentage: {currentFederation.youth_percentage ?? 'N/A'}</p>
                        <h4 className="font-medium mt-4 mb-2">Section by Section Score</h4>
                        <ul className="text-sm pl-4">
                          {sectionList.map(section => (
                            <li key={section.id} className="mb-1 flex justify-between">
                              <span>{section.title}</span>
                              <span>{currentSectionScores[section.id] !== undefined ? `${currentSectionScores[section.id]} out of ${sectionMaxScores[section.id]}` : `N/A out of ${sectionMaxScores[section.id]}`}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    ) : null}
                  </div>
                </CardContent>
              </Card>
            </div>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}