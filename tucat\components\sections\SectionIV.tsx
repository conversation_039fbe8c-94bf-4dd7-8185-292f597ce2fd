"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionIV() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    organizational_structure: "",
    roles_defined: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 4
    const sectionData = assessments.find(a => a.section_number === 4);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        organizational_structure: sectionData.organizational_structure || "",
        roles_defined: sectionData.roles_defined || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(4, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  return (
    <QuestionCard
      title="ORGANIZATIONAL STRUCTURE"
      bengaliTitle="ফেডারেশনের সংগঠনিক কাঠামো"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the clarity and effectiveness of the federation's organizational structure and role definitions.",
        bengali: "ফেডারেশনের সাংগঠনিক কাঠামো এবং ভূমিকার সংজ্ঞার স্পষ্টতা ও কার্যকারিতা মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="Is the organizational and reporting structure of the federation clearly documented and shared with members?"
        bengaliQuestion="ফেডারেশনের সাংগঠনিক এবং প্রতিবেদন কাঠামো কি স্পষ্টভাবে নথিভুক্ত এবং সদস্যদের সাথে শেয়ার করা হয়েছে?"
        value={formData.organizational_structure}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, organizational_structure: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, it is clearly documented and accessible to all members.",
            bengaliLabel: "হ্যাঁ, এটি স্পষ্টভাবে নথিভুক্ত এবং সকল সদস্যের জন্য সহজলভ্য।"
          },
          {
            value: "b",
            label: "Somewhat – there is some documentation, but it is not easily accessible.",
            bengaliLabel: "কিছুটা -নথিপত্র রয়েছে, তবে তা সহজে প্রবেশযোগ্য নয়।"
          },
          {
            value: "c",
            label: "No, the structure is not documented or shared with members.",
            bengaliLabel: "না, কাঠামো নথিভুক্ত বা সদস্যদের সাথে শেয়ার করা হয়নি।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={2}
        question="Are roles and responsibilities within the federation clearly defined?"
        bengaliQuestion="ফেডারেশনের ভিতরে ভূমিকা ও দায়িত্ব কি স্পষ্টভাবে নির্ধারিত?"
        value={formData.roles_defined}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, roles_defined: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, roles are clearly defined and understood by all members.",
            bengaliLabel: "হ্যাঁ, ভূমিকা স্পষ্টভাবে নির্ধারিত এবং সকল সদস্য সেটি বুঝেছে।"
          },
          {
            value: "b",
            label: "Somewhat – roles are defined but not all members are aware of them.",
            bengaliLabel: "কিছুটা ভূমিকা নির্ধারিত _, কিন্তু সব সদস্য জানে না বা জানেন না।"
          },
          {
            value: "c",
            label: "No, roles are unclear or not defined.",
            bengaliLabel: "না, ভূমিকা অস্পষ্ট বা নির্ধারিত নয়।"
          }
        ]}
      />
    </QuestionCard>
  );
}