"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import Link from "next/link";
import { useAuth } from "./AuthContext";
import { UserNav } from "./UserNav";
import { ThemeToggle } from "@/components/ui/theme-toggle";

export function Hero() {
  const { isAuthenticated, isLoading } = useAuth();
  return (
    <section className="relative px-4 sm:px-6 lg:px-8 bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 min-h-[calc(100vh-80px)]">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      <div className="mx-auto max-w-7xl py-16 sm:py-24 lg:py-32">
        <div className="text-center">
          <h1 className="text-3xl sm:text-4xl lg:text-6xl font-bold tracking-tight text-gray-900 dark:text-white leading-tight">
            Trade Union Federation Capacity Assessment Tool
          </h1>
          <p className="mt-4 sm:mt-6 text-base sm:text-lg leading-7 sm:leading-8 text-gray-600 dark:text-gray-300 max-w-3xl mx-auto px-2 sm:px-4 transition-colors duration-200">
            This tool is designed by 'Growing Together,' a Research, Knowledge Management, and Capacity Building Firm registered in Bangladesh, to help Trade Union Federation leaders assess the capacity of their federations. It is structured into different sections, with each question offering answer options that reflect the federation's current state, allowing leaders to select the option that best describes their situation.
          </p>
          <p className="mt-3 sm:mt-4 text-sm sm:text-md text-gray-500 dark:text-gray-400 px-2 sm:px-4 transition-colors duration-200">
            এই সরঞ্জামটি ডিজাইন করেছে 'Growing Together' — যা বাংলাদেশে নিবন্ধিত একটি গবেষণা, জ্ঞান ব্যবস্থাপনা এবং সক্ষমতা উন্নয়ন সংস্থা। এটি ট্রেড ইউনিয়ন ফেডারেশনের নেতাদের তাদের ফেডারেশনের সক্ষমতা মূল্যায়ন করতে সহায়তা করার জন্য তৈরি করা হয়েছে। এটি বিভিন্ন বিভাগে বিভক্ত, যেখানে প্রতিটি প্রশ্নের জন্য উত্তরের বিকল্প দেওয়া হয়েছে যা ফেডারেশনের বর্তমান অবস্থাকে প্রতিফলিত করে। নেতারা সেই বিকল্পটি নির্বাচন করতে পারেন যা তাদের ফেডারেশনের পরিস্থিতিকে সবচেয়ে ভালোভাবে বর্ণনা করে।
          </p>
          <div className="mt-8 sm:mt-10 px-4 flex flex-col sm:flex-row gap-4 justify-center">
            {isLoading ? (
              <div className="h-8 w-8 animate-spin rounded-full border-b-2 border-t-2 border-primary mx-auto"></div>
            ) : isAuthenticated ? (
              <>
                <Link href="/assessment">
                  <Button size="lg" className="w-full sm:w-auto text-base sm:text-lg px-6 sm:px-8 py-5 sm:py-6">
                    Continue Assessment
                    <span className="block text-md sm:text-md mt-1 pl-2">(মূল্যায়ন চালিয়ে যান)</span>
                  </Button>
                </Link>
                <Link href="/dashboard">
                  <Button size="lg" variant="outline" className="w-full sm:w-auto text-base sm:text-lg px-6 sm:px-8 py-5 sm:py-6">
                    View Dashboard
                    <span className="block text-md sm:text-md mt-1 pl-2">(ড্যাশবোর্ড দেখুন)</span>
                  </Button>
                </Link>
                <div className="mt-2 sm:mt-0">
                  <UserNav />
                </div>
              </>
            ) : (
              <>
                <Link href="/login">
                  <Button size="lg" className="w-full sm:w-auto text-base sm:text-lg px-6 sm:px-8 py-5 sm:py-6">
                    Login
                    <span className="block text-md sm:text-md mt-1 pl-2">(লগইন)</span>
                  </Button>
                </Link>
                <Link href="/register">
                  <Button size="lg" variant="outline" className="w-full sm:w-auto text-base sm:text-lg px-6 sm:px-8 py-5 sm:py-6">
                    Register
                    <span className="block text-md sm:text-md mt-1 pl-2">(নিবন্ধন)</span>
                  </Button>
                </Link>
              </>
            )}
          </div>
        </div>
      </div>
    </section>
  );
}