"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { YesNoQuestionGroup } from "@/components/questions/YesNoQuestionGroup";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionXI() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    audit_system_quality: "",
    requires_annual_audit: "",
    regularly_audited: "",
    auditor_selection: "",
    audit_manager: "",
    implements_recommendations: "",
    shares_reports: "",
    report_provides_info: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 11
    const sectionData = assessments.find(a => a.section_number === 11);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        audit_system_quality: sectionData.audit_system_quality || "",
        requires_annual_audit: sectionData.requires_annual_audit || "",
        regularly_audited: sectionData.regularly_audited || "",
        auditor_selection: sectionData.auditor_selection || "",
        audit_manager: sectionData.audit_manager || "",
        implements_recommendations: sectionData.implements_recommendations || "",
        shares_reports: sectionData.shares_reports || "",
        report_provides_info: sectionData.report_provides_info || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please fill out all the questions before saving.");
        return;
      }

      await saveAssessment(11, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  const handleYesNoChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <QuestionCard
      title="AUDIT"
      bengaliTitle="নিরীক্ষা"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the federation's audit practices and compliance with statutory requirements.",
        bengali: "ফেডারেশনের নিরীক্ষা অনুশীলন এবং আইনি প্রয়োজনীয়তার সাথে সম্মতি মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="How would you rate the Federation's audit system?"
        bengaliQuestion="ফেডারেশনের নিরীক্ষা ব্যবস্থা কেমন?"
        value={formData.audit_system_quality}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, audit_system_quality: value }))
        }
        options={[
          {
            value: "1",
            label: "The Federation has no internal or external auditing system",
            bengaliLabel: "কোনো অভ্যন্তরীণ বা বহিরাগত নিরীক্ষা ব্যবস্থা নেই।"
          },
          {
            value: "2",
            label: "The Federation has a basic audit/review system, but auditing requirements and deadlines are not adhered to",
            bengaliLabel: "একটি মৌলিক নিরীক্ষা/পর্যালোচনা ব্যবস্থা রয়েছে, তবে নিরীক্ষার প্রয়োজনীয়তা এবং সময়সীমা মানা হয়না।"
          },
          {
            value: "3",
            label: "The Federation has a good system for managing audits; audit findings and recommendations are generally addressed",
            bengaliLabel: "নিরীক্ষা পরিচালনার জন্য একটি ভালো ব্যবস্থা রয়েছে; নিরীক্ষার ফলাফল এবং সুপারিশ সাধারণত বাস্তবায়িত হয়।"
          },
          {
            value: "4",
            label: "The Federation has a complete and appropriate system for managing audits; audit findings and recommendations are systematically addressed",
            bengaliLabel: "নিরীক্ষা পরিচালনার জন্য একটি সম্পূর্ণ এবং উপযুক্ত ব্যবস্থা রয়েছে; নিরীক্ষার ফলাফল এবং সুপারিশ পদ্ধতিগতভাবে বাস্তবায়িত হয়।"
          }
        ]}
      />

      <div className="border rounded-lg overflow-hidden bg-blue-50/50">
        <div className="p-4 border-b">
          <div className="font-semibold">Audit Assessment</div>
          <div className="text-sm">নিরীক্ষা মূল্যায়ন</div>
        </div>
        <table className="w-full border-collapse">
          <thead className="bg-muted/50">
            <tr className="border-b">
              <th className="p-3 text-left">#</th>
              <th className="p-3 text-left">Question</th>
              <th className="p-3 text-center w-24">Yes</th>
              <th className="p-3 text-center w-24">No</th>
              <th className="p-3 text-center w-24">N/A</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">2.</td>
              <td className="p-3 border-r">
                <div>Does your Federation's constitution require an annual audit?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশনের সংবিধান অনুসারে কি বার্ষিক নিরীক্ষা প্রয়োজন?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="requires_annual_audit"
                      value="yes"
                      checked={formData.requires_annual_audit === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, requires_annual_audit: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="requires_annual_audit"
                      value="no"
                      checked={formData.requires_annual_audit === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, requires_annual_audit: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="requires_annual_audit"
                      value="na"
                      checked={formData.requires_annual_audit === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, requires_annual_audit: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">3.</td>
              <td className="p-3 border-r">
                <div>Is your Federation regularly audited?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি নিয়মিত নিরীক্ষিত হয়?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="regularly_audited"
                      value="yes"
                      checked={formData.regularly_audited === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, regularly_audited: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="regularly_audited"
                      value="no"
                      checked={formData.regularly_audited === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, regularly_audited: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="regularly_audited"
                      value="na"
                      checked={formData.regularly_audited === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, regularly_audited: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">4.</td>
              <td className="p-3 border-r">
                <div>Does your Federation select the auditor through a competitive process?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি প্রতিযোগিতামূলক প্রক্রিয়ার মাধ্যমে নিরীক্ষক নির্বাচন করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="auditor_selection"
                      value="yes"
                      checked={formData.auditor_selection === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, auditor_selection: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="auditor_selection"
                      value="no"
                      checked={formData.auditor_selection === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, auditor_selection: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="auditor_selection"
                      value="na"
                      checked={formData.auditor_selection === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, auditor_selection: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">5.</td>
              <td className="p-3 border-r">
                <div>Does your Federation have a designated person to manage the audit process?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশনের কি নিরীক্ষা প্রক্রিয়ায় সহায়তা করার জন্য একজন নির্দিষ্ট ব্যক্তি আছে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="audit_manager"
                      value="yes"
                      checked={formData.audit_manager === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, audit_manager: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="audit_manager"
                      value="no"
                      checked={formData.audit_manager === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, audit_manager: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="audit_manager"
                      value="na"
                      checked={formData.audit_manager === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, audit_manager: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">6.</td>
              <td className="p-3 border-r">
                <div>Does your Federation implement audit recommendations?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি নিরীক্ষা সুপারিশগুলি বাস্তবায়ন করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="implements_recommendations"
                      value="yes"
                      checked={formData.implements_recommendations === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, implements_recommendations: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="implements_recommendations"
                      value="no"
                      checked={formData.implements_recommendations === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, implements_recommendations: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="implements_recommendations"
                      value="na"
                      checked={formData.implements_recommendations === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, implements_recommendations: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">7.</td>
              <td className="p-3 border-r">
                <div>Does your Federation share audit reports with members?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি সদস্যদের সাথে নিরীক্ষা প্রতিবেদন শেয়ার করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="shares_reports"
                      value="yes"
                      checked={formData.shares_reports === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, shares_reports: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="shares_reports"
                      value="no"
                      checked={formData.shares_reports === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, shares_reports: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="shares_reports"
                      value="na"
                      checked={formData.shares_reports === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, shares_reports: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">8.</td>
              <td className="p-3 border-r">
                <div>Does the audit report provide information on the Federation's financial health and compliance with laws and regulations?</div>
                <div className="text-sm text-muted-foreground">
                  নিরীক্ষা প্রতিবেদন কি ফেডারেশনের আর্থিক স্বাস্থ্য এবং আইন ও প্রবিধানের সাথে সম্মতি সম্পর্কে তথ্য প্রদান করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="report_provides_info"
                      value="yes"
                      checked={formData.report_provides_info === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, report_provides_info: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="report_provides_info"
                      value="no"
                      checked={formData.report_provides_info === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, report_provides_info: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="report_provides_info"
                      value="na"
                      checked={formData.report_provides_info === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, report_provides_info: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </QuestionCard>
  );
}