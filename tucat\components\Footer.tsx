import { Building2, Globe, Mail, Phone } from "lucide-react";
import { Separator } from "@/components/ui/separator";

export function Footer() {
  return (
    <footer className="mt-auto bg-gradient-to-b from-white to-gray-50 dark:from-gray-900 dark:to-gray-800 border-t transition-colors duration-200">
      <div className="mx-auto max-w-7xl px-6 py-8 md:py-12 lg:py-16 lg:px-8">
        <div className="grid gap-8 md:grid-cols-2">
          {/* Left Column - Company Info */}
          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold text-gray-900 dark:text-white">Growing Together OPC</h3>
              <p className="mt-2 text-sm leading-6 text-gray-600 dark:text-gray-300">
                A research, capacity-development, knowledge-management, and policy-advocacy consulting firm.
              </p>
            </div>
              <div className="space-y-2">
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  Thank you for using our assessment tool!
                </p>
                <p className="text-sm text-gray-600 dark:text-gray-300">
                  We are available to extend our cooperation should you require any further assistance.
                </p>
                <div>
                <h3 className="text-md font-semibold text-gray-900 dark:text-white">Contact Information</h3>
                <div className="mt-4 space-y-4">
                  <div className="flex items-center gap-3">
                    <Building2 className="h-5 w-5 text-gray-500 dark:text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900 dark:text-white">M. Anowar Hossain</p>
                      <p className="text-sm text-gray-600 dark:text-gray-300">Executive Chairman</p>
                    </div>
                  </div>
                  <a
                    href="http://www.growingtogether-int.org"
                    target="_blank"
                    rel="noopener noreferrer"
                    className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    <Globe className="h-5 w-5" />
                    www.growingtogether-int.org
                  </a>
                  <div className="flex items-center gap-3">
                    <a
                      href="tel:+8801714039746"
                      className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                      >
                      <Phone className="h-5 w-5" />
                      01714039746
                    </a>
                  </div>
                  <a
                    href="mailto:<EMAIL>"
                    className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-300 hover:text-blue-600 dark:hover:text-blue-400 transition-colors"
                  >
                    <Mail className="h-5 w-5" />
                    <EMAIL>
                  </a>
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Contact Info */}
          <div className="space-y-6 md:pl-8 md:border-l md:border-gray-200 md:dark:border-gray-700">
            <div>
              <div className="mt-4 space-y-4">
                <div className="flex items-center gap-3">
                  <div>
                    <p className="text-md font-medium text-gray-900 dark:text-white">Our sincere gratitude</p>
                  </div>
                </div>
                {/* Add logos here */}
                <div className="mt-4 grid grid-cols-2 gap-4">
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/german_cooperation_bangladesh_ie3tbs.png" alt="German Cooperation Bangladesh" className="h-18 w-auto mx-auto" />
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/International_Labour_Organization_lyixad.png" alt="International Labour Organization" className="h-18 w-auto mx-auto" />
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/growing-together-opc_jij5fp.png" alt="Growing Together OPC" className="h-18 w-auto mx-auto" />
                  <img src="https://res.cloudinary.com/drakcyyri/image/upload/government-of-the-peroples-republic-of-bangladesh_qghlkq.png" alt="Government of the people's republic of Bangladesh" className="h-18 w-auto mx-auto" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <Separator className="my-6 md:my-8" />

        <div className="text-center">
          <p className="text-xs text-gray-500 dark:text-gray-400">
            © {new Date().getFullYear()} Growing Together. All rights reserved. Developed by itoc International.
          </p>
        </div>
      </div>
    </footer>
  );
}