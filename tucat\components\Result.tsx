"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON>eader, CardTitle, CardDescription } from "@/components/ui/card";
import { useAssessmentContext } from "./AssessmentContext";
import { Button } from "@/components/ui/button";
import { Download, Share2 } from "lucide-react";
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  ChartData,
} from 'chart.js';
import { Doughnut } from 'react-chartjs-2';
import { Badge } from "@/components/ui/badge";

ChartJS.register(ArcElement, Tooltip, Legend);

export function Result() {
  const { score, federation } = useAssessmentContext();

  if (!score) return null;

  const chartData: ChartData<"doughnut"> = {
    labels: ['Score', 'Remaining'],
    datasets: [
      {
        data: [score.total, score.maxPossible - score.total],
        backgroundColor: [
          'hsl(var(--chart-1))',
          'hsl(var(--muted))',
        ],
        borderWidth: 0,
      },
    ],
  };

  // Determine score category for badge color
  const getScoreCategory = () => {
    if (score.percentage >= 80) return "success";
    if (score.percentage >= 60) return "warning";
    return "destructive";
  };

  return (
    <Card className="shadow-md transition-all duration-200 hover:shadow-lg">
      <CardHeader className="text-center pb-2">
        <CardTitle className="text-2xl font-bold">Assessment Results</CardTitle>
        <CardDescription>
          {federation.name} Federation Assessment Score
        </CardDescription>
      </CardHeader>
      <CardContent>

        <div className="grid md:grid-cols-2 gap-8 items-center">
          <div className="w-64 mx-auto relative">
            <Doughnut
              data={chartData}
              options={{
                cutout: '70%',
                plugins: {
                  legend: {
                    display: false,
                  },
                  tooltip: {
                    displayColors: false,
                    callbacks: {
                      label: (context) => {
                        const label = context.label || '';
                        const value = context.raw || 0;
                        return `${label}: ${value} points`;
                      }
                    }
                  }
                },
                animation: {
                  animateScale: true,
                  animateRotate: true
                }
              }}
            />
            <div className="text-center mt-4">
              <Badge variant={score.percentage >= 80 ? "success" : "destructive"} className="mb-2 text-sm px-3 py-1">
                {Math.round(score.percentage)}%
              </Badge>
              <div className="text-4xl font-bold">
                {Math.round(score.percentage)}%
              </div>
              <div className="text-sm text-muted-foreground">
                {score.total} out of {score.maxPossible} points
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div>
              <h3 className="text-xl font-semibold mb-2">Assessment Summary</h3>
              <p className="text-muted-foreground">{score.message}</p>
            </div>

            <div className="space-y-2">
              <h4 className="font-medium">Key Metrics:</h4>
              <ul className="list-disc list-inside space-y-1 text-muted-foreground">
                <li>Total Score: {score.total} points</li>
                <li>Maximum Possible: {score.maxPossible} points</li>
                <li>Overall Percentage: {Math.round(score.percentage)}%</li>
              </ul>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}