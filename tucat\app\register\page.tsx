"use client";

import { useState, useEffect } from 'react';
import { useAuth } from '@/components/AuthContext';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from '@/components/ui/card';
import { Label } from '@/components/ui/label';
import Link from 'next/link';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { UserRole } from '@/lib/types';
import { ThemeToggle } from '@/components/ui/theme-toggle';
import { Separator } from '@/components/ui/separator';
import { Progress } from '@/components/ui/progress';
import { cn } from '@/lib/utils';
import {
  UserPlus,
  AlertCircle,
  Check,
  X,
  Building,
  ArrowRight,
  ArrowLeft,
  Mail,
  Lock,
  User,
  Eye,
  EyeOff
} from 'lucide-react';

type Step = {
  title: string;
  description: string;
};

const steps: Step[] = [
  {
    title: 'Personal Info',
    description: 'Start with your basic information'
  },
  {
    title: 'Account Security',
    description: 'Set up your password'
  },
  {
    title: 'Confirmation',
    description: 'Review your information'
  }
];

export default function RegisterPage() {
  const { register, isLoading, error } = useAuth();
  const [currentStep, setCurrentStep] = useState(0);
  const [mounted, setMounted] = useState(false);
  const [formData, setFormData] = useState({
    username: '',
    email: '',
    password: '',
    confirmPassword: '',
    role: UserRole.FEDERATION_MEMBER,
  });
  const [formError, setFormError] = useState<string | null>(null);
  const [passwordStrength, setPasswordStrength] = useState(0);
  const [passwordFeedback, setPasswordFeedback] = useState('');
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  useEffect(() => {
    setMounted(true);
    return () => setMounted(false);
  }, []);

  useEffect(() => {
    if (!formData.password) {
      setPasswordStrength(0);
      setPasswordFeedback('');
      return;
    }

    const checks = [
      formData.password.length >= 8,
      /[A-Z]/.test(formData.password),
      /[a-z]/.test(formData.password),
      /[0-9]/.test(formData.password),
      /[^A-Za-z0-9]/.test(formData.password)
    ];

    const strength = (checks.filter(Boolean).length / checks.length) * 100;
    setPasswordStrength(strength);

    if (strength < 40) {
      setPasswordFeedback('Weak - Add more variety');
    } else if (strength < 80) {
      setPasswordFeedback('Moderate - Getting better');
    } else {
      setPasswordFeedback('Strong - Excellent choice');
    }
  }, [formData.password]);

  const validateStep = () => {
    setFormError(null);

    switch (currentStep) {
      case 0:
        if (!formData.username.trim()) {
          setFormError('Please enter your username');
          return false;
        }
        if (formData.username.trim().length < 3) {
          setFormError('Username must be at least 3 characters long');
          return false;
        }
        if (!formData.email.trim()) {
          setFormError('Please enter your email');
          return false;
        }
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(formData.email)) {
          setFormError('Invalid email format. Please enter a valid email address (e.g., <EMAIL>)');
          return false;
        }
        break;
      case 1:
        if (!formData.password) {
          setFormError('Please enter a password');
          return false;
        }
        if (passwordStrength < 40) {
          setFormError('Password is too weak. Please include uppercase letters, lowercase letters, numbers, and special characters.');
          return false;
        }
        if (!formData.confirmPassword) {
          setFormError('Please confirm your password');
          return false;
        }
        if (formData.password !== formData.confirmPassword) {
          setFormError('Passwords do not match. Please make sure both passwords are identical.');
          return false;
        }
        break;
    }
    return true;
  };

  const handleNext = () => {
    if (validateStep()) {
      setCurrentStep((prev) => Math.min(prev + 1, steps.length - 1));
    }
  };

  const handleBack = () => {
    setCurrentStep((prev) => Math.max(prev - 1, 0));
    setFormError(null);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (validateStep()) {
      await register(
        formData.username,
        formData.email,
        formData.password,
        '',
        formData.role
      );
    }
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 0:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="username">Username</Label>
              <div className="relative">
                <User className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="username"
                  placeholder="Your name"
                  value={formData.username}
                  onChange={(e) => setFormData({ ...formData, username: e.target.value })}
                  className="pl-10 transition-all duration-200"
                  required
                />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <div className="relative">
                <Mail className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={(e) => setFormData({ ...formData, email: e.target.value })}
                  className="pl-10 transition-all duration-200"
                  required
                />
              </div>
            </div>
          </div>
        );
      case 1:
        return (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="password">Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="password"
                  type={showPassword ? "text" : "password"}
                  value={formData.password}
                  onChange={(e) => setFormData({ ...formData, password: e.target.value })}
                  className="pl-10 pr-10 transition-all duration-200"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
              {formData.password && (
                <div className="mt-2 space-y-1">
                  <div className="flex justify-between items-center text-xs">
                    <span className={cn(
                      passwordStrength >= 80 ? 'text-green-500' :
                      passwordStrength >= 40 ? 'text-yellow-500' :
                      'text-red-500'
                    )}>{passwordFeedback}</span>
                    <span className="flex items-center">
                      {passwordStrength >= 80 ? (
                        <Check className="h-3 w-3 text-green-500 mr-1" />
                      ) : passwordStrength >= 40 ? (
                        <span className="text-yellow-500">●</span>
                      ) : (
                        <X className="h-3 w-3 text-red-500 mr-1" />
                      )}
                      {Math.round(passwordStrength)}%
                    </span>
                  </div>
                  <Progress
                    value={passwordStrength}
                    className={cn(
                      'h-1',
                      passwordStrength >= 80 ? 'bg-green-500' :
                      passwordStrength >= 40 ? 'bg-yellow-500' :
                      'bg-red-500'
                    )}
                  />
                </div>
              )}
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirm Password</Label>
              <div className="relative">
                <Lock className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  id="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  value={formData.confirmPassword}
                  onChange={(e) => setFormData({ ...formData, confirmPassword: e.target.value })}
                  className="pl-10 pr-10 transition-all duration-200"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground transition-colors"
                  tabIndex={-1}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>
            </div>
          </div>
        );
      case 2:
        return (
          <div className="space-y-4">
            <div className="rounded-lg border p-4 space-y-3">
              <div className="space-y-1">
                <Label className="text-sm text-muted-foreground">Username</Label>
                <p className="font-medium">{formData.username}</p>
              </div>
              <Separator />
              <div className="space-y-1">
                <Label className="text-sm text-muted-foreground">Email</Label>
                <p className="font-medium">{formData.email}</p>
              </div>
            </div>
            <p className="text-sm text-muted-foreground">
              After registration, you will be able to select your federation in the assessment section.
            </p>
          </div>
        );
    }
  };

  return (
    <div className="flex min-h-screen bg-gradient-to-b from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 transition-colors duration-200">
      {/* Theme toggle in the top right corner */}
      <div className="absolute top-4 right-4 z-10">
        <ThemeToggle />
      </div>

      {/* Left side - Illustration/Brand section */}
      <div className="hidden lg:flex lg:w-1/2 bg-primary/10 dark:bg-primary/5 flex-col items-center justify-center p-12 relative overflow-hidden">
        <div className={cn(
          "flex flex-col items-center justify-center space-y-8 z-10 transition-all duration-500",
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        )}>
          <div className="rounded-full bg-primary/20 p-6 backdrop-blur-sm">
            <Building className="h-16 w-16 text-primary" />
          </div>
          <div className="space-y-4 text-center">
            <h1 className="text-4xl font-bold tracking-tight">Trade Union Assessment</h1>
            <p className="text-lg text-muted-foreground max-w-md">
              Join your federation and start managing your assessment progress
            </p>
          </div>

          {/* Decorative elements */}
          <div className="mt-8 space-y-2">
            <div className="flex items-center space-x-2">
              <div className="h-1 w-1 rounded-full bg-primary"></div>
              <div className="h-1 w-3 rounded-full bg-primary"></div>
              <div className="h-1 w-6 rounded-full bg-primary"></div>
              <div className="h-1 w-3 rounded-full bg-primary"></div>
              <div className="h-1 w-1 rounded-full bg-primary"></div>
            </div>
          </div>
        </div>

        {/* Background decorative elements */}
        <div className="absolute top-0 left-0 right-0 bottom-0 opacity-20">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-primary/30 rounded-full filter blur-3xl"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-primary/30 rounded-full filter blur-3xl"></div>
        </div>
      </div>

      {/* Right side - Registration form */}
      <div className="w-full lg:w-1/2 flex items-center justify-center p-4 md:p-8">
        <Card className={cn(
          "w-full max-w-md border-t-4 border-t-primary transition-all duration-500",
          "shadow-lg hover:shadow-xl",
          mounted ? "opacity-100 translate-y-0" : "opacity-0 translate-y-10"
        )}>
          <CardHeader className="space-y-2">
            <div className="flex justify-between items-center">
              <CardTitle className="text-2xl font-bold">Create an account</CardTitle>
              <UserPlus className="h-6 w-6 text-primary" />
            </div>
            <CardDescription>
              {steps[currentStep].description}
            </CardDescription>

            {/* Step indicator */}
            <div className="flex justify-between items-center mt-4">
              {steps.map((step, index) => (
                <div key={step.title} className="flex items-center">
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium",
                    currentStep === index
                      ? "bg-primary text-primary-foreground"
                      : currentStep > index
                      ? "bg-primary/20 text-primary"
                      : "bg-muted text-muted-foreground"
                  )}>
                    {index + 1}
                  </div>
                  {index < steps.length - 1 && (
                    <div className={cn(
                      "h-1 w-12",
                      currentStep > index
                        ? "bg-primary"
                        : "bg-muted"
                    )} />
                  )}
                </div>
              ))}
            </div>
          </CardHeader>

          <form onSubmit={handleSubmit}>
            <CardContent className="space-y-4">
              {(error || formError) && (
                <Alert variant="destructive" className="animate-fadeIn">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription className="font-medium">{formError || error}</AlertDescription>
                </Alert>
              )}

              {renderStepContent()}
            </CardContent>

            <CardFooter className="flex flex-col space-y-4">
              <div className="flex gap-2 w-full">
                {currentStep > 0 && (
                  <Button
                    type="button"
                    variant="outline"
                    onClick={handleBack}
                    className="flex-1"
                  >
                    <ArrowLeft className="h-4 w-4 mr-2" />
                    Back
                  </Button>
                )}
                {currentStep < steps.length - 1 ? (
                  <Button
                    type="button"
                    onClick={handleNext}
                    className="flex-1"
                  >
                    Next
                    <ArrowRight className="h-4 w-4 ml-2" />
                  </Button>
                ) : (
                  <Button
                    type="submit"
                    className="flex-1"
                    disabled={isLoading}
                  >
                    {isLoading ? 'Creating account...' : 'Complete Registration'}
                  </Button>
                )}
              </div>

              <div className="text-center text-sm">
                Already have an account?{' '}
                <Link href="/login" className="font-medium text-primary hover:underline transition-colors">
                  Sign in
                </Link>
              </div>
            </CardFooter>
          </form>
        </Card>
      </div>
    </div>
  );
}