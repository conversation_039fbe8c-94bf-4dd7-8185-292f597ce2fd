"use client";

import { useAssessmentContext } from "./AssessmentContext";
import { sections } from "./AssessmentLayout";
import { 
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ChevronRight, CheckCircle2 } from "lucide-react";
import { cn } from "@/lib/utils";

export function AssessmentBreadcrumb() {
  const { currentSection, setCurrentSection, assessments } = useAssessmentContext();
  
  // Get completed sections
  const completedSections = new Set(assessments.map(a => a.section_number));
  
  // Get previous and next section numbers
  const prevSection = currentSection > 1 ? currentSection - 1 : null;
  const nextSection = currentSection < sections.length ? currentSection + 1 : null;
  
  // Handle navigation
  const handleNavigation = (sectionId: number) => {
    setCurrentSection(sectionId);
  };
  
  return (
    <Breadcrumb className="mb-4 animate-fadeIn">
      <BreadcrumbList className="flex-wrap">
        <BreadcrumbItem>
          <BreadcrumbLink 
            onClick={() => handleNavigation(1)}
            className="cursor-pointer flex items-center gap-1"
          >
            {completedSections.has(1) && (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            )}
            <span>Section I</span>
          </BreadcrumbLink>
        </BreadcrumbItem>
        <BreadcrumbSeparator>
          <ChevronRight className="h-4 w-4" />
        </BreadcrumbSeparator>
        
        {/* Show previous section if not the first */}
        {prevSection && prevSection !== 1 && (
          <>
            <BreadcrumbItem>
              <BreadcrumbLink 
                onClick={() => handleNavigation(prevSection)}
                className="cursor-pointer flex items-center gap-1"
              >
                {completedSections.has(prevSection) && (
                  <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                )}
                <span>Section {prevSection}</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
          </>
        )}
        
        {/* Current section */}
        <BreadcrumbItem>
          <BreadcrumbPage className="flex items-center gap-1 font-medium">
            {completedSections.has(currentSection) && (
              <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
            )}
            <span>Section {currentSection}</span>
          </BreadcrumbPage>
        </BreadcrumbItem>
        
        {/* Show next section if not the last */}
        {nextSection && (
          <>
            <BreadcrumbSeparator>
              <ChevronRight className="h-4 w-4" />
            </BreadcrumbSeparator>
            <BreadcrumbItem>
              <BreadcrumbLink 
                onClick={() => handleNavigation(nextSection)}
                className={cn(
                  "cursor-pointer flex items-center gap-1",
                  !completedSections.has(currentSection) && "text-muted-foreground pointer-events-none"
                )}
              >
                {completedSections.has(nextSection) && (
                  <CheckCircle2 className="h-3.5 w-3.5 text-green-500" />
                )}
                <span>Section {nextSection}</span>
              </BreadcrumbLink>
            </BreadcrumbItem>
          </>
        )}
      </BreadcrumbList>
    </Breadcrumb>
  );
}
