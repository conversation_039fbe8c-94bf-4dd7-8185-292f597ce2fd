"use client";

import { useDashboardContext } from "@/components/dashboard/DashboardProvider";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Users, UserPlus, UserMinus, UsersRound } from "lucide-react";
import { Skeleton } from "@/components/ui/skeleton";

export function CommitteeInfoCard() {
  const { committees, isLoading } = useDashboardContext();

  // Calculate totals
  const totalMembers = committees.reduce((sum, committee) => sum + (committee.total_members || 0), 0);
  const totalMaleMembers = committees.reduce((sum, committee) => sum + (committee.male_members || 0), 0);
  const totalFemaleMembers = committees.reduce((sum, committee) => sum + (committee.female_members || 0), 0);

  // Calculate percentages
  const malePercentage = totalMembers ? Math.round((totalMaleMembers / totalMembers) * 100) : 0;
  const femalePercentage = totalMembers ? Math.round((totalFemaleMembers / totalMembers) * 100) : 0;

  if (isLoading) {
    return (
      <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full">
        <CardHeader className="pb-2">
          <Skeleton className="h-6 w-[180px] mb-2" />
          <Skeleton className="h-4 w-[250px]" />
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            <div className="grid grid-cols-3 gap-4">
              {[1, 2, 3].map((i) => (
                <Skeleton key={i} className="h-24 w-full rounded-lg" />
              ))}
            </div>
            <Skeleton className="h-[200px] w-full" />
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
      <CardHeader className="pb-2 bg-muted/10">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <UsersRound className="h-5 w-5 text-primary" />
              Committee Information
            </CardTitle>
            <CardDescription>Details about your federation committees</CardDescription>
          </div>
          <div className="text-xs bg-primary/10 text-primary px-2 py-1 rounded-md font-medium">
            {committees.length} {committees.length === 1 ? 'Committee' : 'Committees'}
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        {committees.length > 0 ? (
          <div className="space-y-6">
            <div className="grid grid-cols-3 gap-4">
              <div className="flex flex-col items-center p-3 bg-muted/30 rounded-lg border border-muted/50 hover:border-muted/80 transition-colors">
                <div className="bg-primary/10 p-2 rounded-full mb-2">
                  <Users className="h-5 w-5 text-primary" />
                </div>
                <span className="text-2xl font-bold">{totalMembers}</span>
                <span className="text-xs text-muted-foreground mt-1">Total Members</span>
              </div>
              <div className="flex flex-col items-center p-3 bg-blue-500/5 rounded-lg border border-muted/50 hover:border-blue-200/50 transition-colors">
                <div className="bg-blue-500/10 p-2 rounded-full mb-2">
                  <UserPlus className="h-5 w-5 text-blue-500" />
                </div>
                <span className="text-2xl font-bold text-blue-600 dark:text-blue-400">{totalMaleMembers}</span>
                <span className="text-xs text-muted-foreground mt-1">Male ({malePercentage}%)</span>
              </div>
              <div className="flex flex-col items-center p-3 bg-pink-500/5 rounded-lg border border-muted/50 hover:border-pink-200/50 transition-colors">
                <div className="bg-pink-500/10 p-2 rounded-full mb-2">
                  <UserMinus className="h-5 w-5 text-pink-500" />
                </div>
                <span className="text-2xl font-bold text-pink-600 dark:text-pink-400">{totalFemaleMembers}</span>
                <span className="text-xs text-muted-foreground mt-1">Female ({femalePercentage}%)</span>
              </div>
            </div>

            <div className="overflow-hidden rounded-lg border border-muted/50 bg-muted/10">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader>
                    <TableRow className="hover:bg-muted/20">
                      <TableHead className="font-semibold">Committee Name</TableHead>
                      <TableHead className="text-right font-semibold">Total</TableHead>
                      <TableHead className="text-right font-semibold">Male</TableHead>
                      <TableHead className="text-right font-semibold">Female</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {committees.map((committee, index) => (
                      <TableRow key={committee.id || index} className="hover:bg-muted/20">
                        <TableCell className="font-medium">{committee.name || `Committee ${index + 1}`}</TableCell>
                        <TableCell className="text-right">{committee.total_members || 0}</TableCell>
                        <TableCell className="text-right text-blue-600 dark:text-blue-400">{committee.male_members || 0}</TableCell>
                        <TableCell className="text-right text-pink-600 dark:text-pink-400">{committee.female_members || 0}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </div>
          </div>
        ) : (
          <div className="flex flex-col items-center justify-center h-64 text-center bg-muted/10 rounded-lg border border-dashed border-muted p-6">
            <div className="bg-muted/30 p-4 rounded-full mb-3">
              <Users className="h-8 w-8 text-muted-foreground" />
            </div>
            <h3 className="text-base font-medium">No Committees Added</h3>
            <p className="text-sm text-muted-foreground mt-2 max-w-xs">
              Add committees in the assessment section to see detailed information here
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
