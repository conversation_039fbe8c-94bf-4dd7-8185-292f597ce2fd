export interface Federation {
  id: string;
  name: string;
  federation_type: string;
  establishment_year: number;
  president_name: string;
  president_age: number;
  secretary_name: string;
  secretary_age: number;
  youth_percentage: string;
  created_at?: string;
  updated_at?: string;
  // Additional properties from API
  totalScore?: number;
  maxScore?: number;
  percentage?: number;
  youthPercentageValue?: number;
  sectionScores?: { [section: number]: number };
}

export interface Committee {
  id: string;
  federation_id: string;
  name: string;
  total_members: number;
  male_members: number;
  female_members: number;
  created_at?: string;
  updated_at?: string;
}

export interface Assessment {
  id: string;
  federation_id: string;
  section_number: number;
  score?: number;
  legal_registration_status?: string;
  registration_authority?: string;
  compliance_review_status?: string;
  vision_mission_status?: string;
  vision_posted?: string;
  vision_in_documents?: string;
  vision_for_planning?: string;
  leadership_effectiveness?: string;
  decision_making?: string;
  emerging_issues_handling?: string;
  leadership_development_plan?: string;
  organizational_structure?: string;
  roles_defined?: string;

  // Section V properties
  management_approach?: string;
  authority_delegation?: string;
  decision_making_process?: string;
  deputy_availability?: string;
  transition_plan?: string;

  // Section VI properties
  workers_involvement_level?: string;
  involve_program_activities?: string;
  involve_leaders_orientation?: string;
  solicit_feedback?: string;
  regular_interaction?: string;
  share_results?: string;

  // Section VII properties
  cultural_gender_consideration?: string;
  consider_local_culture?: string;
  documented_guidelines?: string;
  provide_training?: string;
  use_assessment_findings?: string;

  // Section VIII properties
  representation_effectiveness?: string;
  member_involvement?: string;
  bargaining_strategy?: string;

  // Section IX properties
  communication_effectiveness?: string;
  member_engagement?: string;
  participation_opportunities?: string;
  feedback_collection?: string;

  // Section X properties
  fee_collection?: string;
  financial_management?: string;
  financial_planning?: string;
  financial_system_quality?: string;
  has_cash_system?: string;
  uses_accounting_software?: string;
  has_chart_accounts?: string;
  reconciles_monthly?: string;

  // Section XI properties
  audit_system_quality?: string;
  requires_annual_audit?: string;
  regularly_audited?: string;
  auditor_selection?: string;
  audit_manager?: string;
  implements_recommendations?: string;
  shares_reports?: string;
  report_provides_info?: string;

  created_at?: string;
  updated_at?: string;
}

export interface AssessmentScore {
  total: number;
  maxPossible: number;
  percentage: number;
  message: string;
}

export const SCORE_MESSAGES = {
  excellent: "Excellent! Your federation demonstrates strong organizational capacity and effective leadership.",
  good: "Good progress! Your federation shows promising development with room for strategic improvements.",
  fair: "Fair standing. Consider focusing on key areas for development to strengthen your federation.",
  needsImprovement: "Your federation would benefit from focused capacity building in several key areas."
}

// User Authentication and Authorization Types
export interface User {
  id: string;
  username: string;
  email: string;
  password: string; // Hashed password
  federation_id: string; // Federation this user belongs to
  role: UserRole;
  created_at?: string;
  updated_at?: string;
}

export enum UserRole {
  ADMIN = "admin",
  FEDERATION_ADMIN = "federation_admin",
  FEDERATION_MEMBER = "federation_member",
  GUEST = "guest"
}

export interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  error: string | null;
}