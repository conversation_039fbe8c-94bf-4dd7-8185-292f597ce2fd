"use client";

import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { TextQuestion } from "@/components/questions/TextQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";
import { useState, useEffect } from "react";

export function SectionII() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    legal_registration_status: "",
    registration_authority: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 2
    const sectionData = assessments.find(a => a.section_number === 2);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        legal_registration_status: sectionData.legal_registration_status || "",
        registration_authority: sectionData.registration_authority || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(2, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  return (
    <QuestionCard
      title="LEGAL STATUS AND COMPLIANCE"
      bengaliTitle="আইনি অবস্থা এবং প্রতিপালন"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the federation's legal status and compliance with relevant labor laws and regulations.",
        bengali: "ফেডারেশনের আইনি অবস্থা এবং প্রাসঙ্গিক শ্রম আইন ও প্রবিধানের সাথে সম্মতি মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="Is the federation legally registered and compliant with local labor laws?"
        bengaliQuestion="ফেডারেশন কি আইনগতভাবে নিবন্ধিত এবং স্থানীয় শ্রম আইন অনুযায়ী সম্মতিপূর্ণ?"
        value={formData.legal_registration_status}
        onChange={(value) =>
          setFormData((prev) => ({
            ...prev,
            legal_registration_status: value,
          }))
        }
        helpText="Legal registration is important for the federation to operate officially and receive benefits from government programs."
        options={[
          {
            value: "a",
            label: "Yes, fully registered and compliant with all relevant laws.",
            bengaliLabel: "হ্যাঁ, সম্পূর্ণ নিবন্ধিত এবং সকল প্রাসঙ্গিক আইনের সাথে সম্মতিপূর্ণ।"
          },
          {
            value: "c",
            label: "No, not registered or compliant.",
            bengaliLabel: "না, নিবন্ধিত বা সম্মতিপূর্ণ নয়।"
          }
        ]}
      />

      <TextQuestion
        questionNumber={2}
        question="Registered with which Authority?"
        bengaliQuestion="কোন কর্তৃপক্ষের সাথে নিবন্ধিত?"
        value={formData.registration_authority}
        onChange={(value) =>
          setFormData((prev) => ({
            ...prev,
            registration_authority: value,
          }))
        }
      />
    </QuestionCard>
  );
}