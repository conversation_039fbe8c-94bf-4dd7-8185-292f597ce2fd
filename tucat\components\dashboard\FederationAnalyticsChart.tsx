"use client";

import { useState } from "react";
import { Card, CardH<PERSON>er, CardTitle, CardDescription, CardContent } from "@/components/ui/card";
import { Building2, <PERSON><PERSON><PERSON> as PieChartIcon, RadarIcon } from "lucide-react";
import { ResponsiveContainer, Tooltip, Legend, Cell, PieChart, Pie, Sector, RadarChart, PolarGrid, PolarAngleAxis, PolarRadiusAxis, Radar } from "recharts";
import type { Federation } from "@/lib/types";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";

interface FederationAnalyticsChartProps {
  federations: Partial<Federation>[];
  selectedFederation: string;
  onSelectFederation: (id: string) => void;
}

type ChartType = "pie" | "radar";

// Custom active shape for interactive pie chart
const renderActiveShape = (props: any) => {
  const { cx, cy, midAngle, innerRadius, outerRadius, startAngle, endAngle, fill, percent, value, name } = props;
  const sin = Math.sin(-midAngle * Math.PI / 180);
  const cos = Math.cos(-midAngle * Math.PI / 180);
  const sx = cx + (outerRadius + 10) * cos;
  const sy = cy + (outerRadius + 10) * sin;
  const mx = cx + (outerRadius + 30) * cos;
  const my = cy + (outerRadius + 30) * sin;
  const ex = mx + (cos >= 0 ? 1 : -1) * 22;
  const ey = my;
  const textAnchor = cos >= 0 ? 'start' : 'end';

  return (
    <g>
      <Sector
        cx={cx}
        cy={cy}
        innerRadius={innerRadius}
        outerRadius={outerRadius + 6}
        startAngle={startAngle}
        endAngle={endAngle}
        fill={fill}
        opacity={0.9}
      />
      <Sector
        cx={cx}
        cy={cy}
        startAngle={startAngle}
        endAngle={endAngle}
        innerRadius={outerRadius + 6}
        outerRadius={outerRadius + 10}
        fill={fill}
        opacity={0.7}
      />
      <path d={`M${sx},${sy}L${mx},${my}L${ex},${ey}`} stroke={fill} fill="none" strokeWidth={2} />
      <circle cx={ex} cy={ey} r={2} fill={fill} stroke="none" />
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} textAnchor={textAnchor} fill="#333" fontSize={12} fontWeight="bold">
        {name}
      </text>
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={18} textAnchor={textAnchor} fill="#666" fontSize={11}>
        Score: {value}
      </text>
      <text x={ex + (cos >= 0 ? 1 : -1) * 12} y={ey} dy={36} textAnchor={textAnchor} fill="#999" fontSize={11}>
        {`(${(percent * 100).toFixed(1)}%)`}
      </text>
    </g>
  );
};

export default function FederationAnalyticsChart({ federations, selectedFederation, onSelectFederation }: FederationAnalyticsChartProps) {
  const [chartType, setChartType] = useState<ChartType>("pie");
  const [activeIndex, setActiveIndex] = useState<number>(0);

  // Calculate analytics for all federations
  const scores = federations
    .map(f => typeof f.totalScore === 'number' ? f.totalScore : null)
    .filter((v): v is number => v !== null);
  const percentages = federations
    .map(f => typeof f.percentage === 'number' ? f.percentage : null)
    .filter((v): v is number => v !== null);

  const totalFederations = federations.length;
  const avgScore = scores.length ? (scores.reduce((a, b) => a + b, 0) / scores.length) : 0;
  const avgPercentage = percentages.length ? (percentages.reduce((a, b) => a + b, 0) / percentages.length) : 0;
  const highestScore = scores.length ? Math.max(...scores) : 0;
  const lowestScore = scores.length ? Math.min(...scores) : 0;
  const highestPercentage = percentages.length ? Math.max(...percentages) : 0;
  const lowestPercentage = percentages.length ? Math.min(...percentages) : 0;

  // Define a list of vibrant colors for the federations
  const colorPalette = [
    '#3b82f6', // Blue
    '#ef4444', // Red
    '#f59e0b', // Amber
    '#10b981', // Green
    '#8b5cf6', // Violet
    '#ec4899', // Pink
    '#06b6d4', // Cyan
    '#f97316', // Orange
    '#84cc16', // Lime
    '#6366f1', // Indigo
    '#14b8a6', // Teal
    '#d946ef', // Fuchsia
    '#0ea5e9', // Sky
    '#a855f7', // Purple
    '#dc2626', // Red-600
    '#2563eb', // Blue-600
    '#7c3aed', // Violet-600
    '#c026d3', // Fuchsia-600
    '#059669', // Emerald-600
    '#ea580c', // Orange-600
  ];

  // Assign a unique color to each federation
  const federationColors: { [key: string]: string } = {};
  federations.forEach((federation, index) => {
    if (federation.id) {
      federationColors[federation.id.toUpperCase()] = colorPalette[index % colorPalette.length];
    }
  });

  // Prepare chart data with federation IDs in uppercase and unique colors
  const chartData = federations.map((f, index) => {
    const fedId = f.id?.toUpperCase() || 'N/A';
    return {
      name: fedId,
      score: typeof f.totalScore === 'number' ? f.totalScore : 0,
      percentage: typeof f.percentage === 'number' ? f.percentage : 0,
      fill: f.id === selectedFederation ?
        '#22c55e' : // Highlight color for selected federation
        federationColors[fedId] || colorPalette[index % colorPalette.length], // Ensure every federation gets a color
      fullMark: 100, // For radar chart
    };
  });

  // Sort data for pie chart (descending by score)
  const pieChartData = [...chartData].sort((a, b) => b.score - a.score);

  // Randomize data for radar chart
  const radarChartData = [...chartData].sort(() => Math.random() - 0.5);

  // Handle pie sector hover
  const onPieEnter = (_: any, index: number) => {
    setActiveIndex(index);
  };

  // Handle pie sector click
  const onPieClick = (_: any, index: number) => {
    // Find the federation ID from the pieChartData entry
    const fedName = pieChartData[index]?.name;
    const federation = federations.find(f => f.id?.toUpperCase() === fedName);

    if (federation?.id) {
      onSelectFederation(federation.id);
    }
  };

  return (
    <Card className="shadow-md transition-all duration-200 hover:shadow-lg h-full overflow-hidden border-muted/40">
      <CardHeader className="pb-2 bg-muted/10">
        <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
          <div>
            <CardTitle className="text-xl flex items-center gap-2">
              <Building2 className="h-5 w-5 text-primary" />
              Federation Analytics
            </CardTitle>
            <CardDescription>
              Overview of all federations' scores and percentages
            </CardDescription>
          </div>
          <Tabs defaultValue="pie" value={chartType} onValueChange={(value) => setChartType(value as ChartType)} className="w-full md:w-auto">
            <TabsList className="grid grid-cols-2 w-full md:w-[200px]">
              <TabsTrigger value="pie" className="flex items-center gap-1">
                <PieChartIcon className="h-3.5 w-3.5" />
                Pie
              </TabsTrigger>
              <TabsTrigger value="radar" className="flex items-center gap-1">
                <RadarIcon className="h-3.5 w-3.5" />
                Radar
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </CardHeader>
      <CardContent className="pt-6">
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-5 gap-4 bg-muted/10 p-4 rounded-lg border border-muted/50">
            <div className="md:col-span-1 space-y-2">
              <p className="text-sm"><span className="font-medium">Total Federations:</span> {totalFederations}</p>
              <p className="text-sm"><span className="font-medium">Average Score:</span> {avgScore.toFixed(2)}</p>
              <p className="text-sm"><span className="font-medium">Average Percentage:</span> {avgPercentage.toFixed(2)}%</p>
              <p className="text-sm"><span className="font-medium">Highest Score:</span> {highestScore} ({highestPercentage}%)</p>
              <p className="text-sm"><span className="font-medium">Lowest Score:</span> {lowestScore} ({lowestPercentage}%)</p>
              <p className="text-sm mt-4 text-muted-foreground">Click on a federation in the chart to select it</p>
            </div>
            <div className="md:col-span-4 h-[450px]">
              <ResponsiveContainer width="100%" height="100%">
                {chartType === "pie" ? (
                  <PieChart margin={{ top: 10, right: 30, left: 30, bottom: 10 }}>
                    <defs>
                      <filter id="shadow" height="200%">
                        <feDropShadow dx="0" dy="3" stdDeviation="3" floodOpacity="0.1"/>
                      </filter>
                    </defs>
                    <Pie
                      activeIndex={activeIndex}
                      activeShape={renderActiveShape}
                      data={pieChartData}
                      cx="50%"
                      cy="50%"
                      innerRadius={80}
                      outerRadius={120}
                      paddingAngle={2}
                      dataKey="score"
                      onMouseEnter={onPieEnter}
                      onClick={onPieClick}
                      cursor="pointer"
                      filter="url(#shadow)"
                    >
                      {pieChartData.map((entry, index) => (
                        <Cell
                          key={`cell-${index}`}
                          fill={entry.fill}
                          stroke="rgba(255,255,255,0.5)"
                          strokeWidth={1}
                        />
                      ))}
                    </Pie>
                    <Tooltip
                      formatter={(value: number, _name: string, props: any) => [
                        `${value.toLocaleString()} (${props.payload.percentage}%)`,
                        props.payload.name
                      ]}
                    />
                    <Legend
                      layout="horizontal"
                      verticalAlign="bottom"
                      align="center"
                      wrapperStyle={{ paddingTop: 20 }}
                    />
                  </PieChart>
                ) : (
                  <RadarChart
                    cx="50%"
                    cy="50%"
                    outerRadius="70%"
                    data={radarChartData}
                    margin={{ top: 20, right: 30, left: 30, bottom: 20 }}
                  >
                    <PolarGrid stroke="#e5e7eb" strokeDasharray="3 3" />
                    <PolarAngleAxis
                      dataKey="name"
                      tick={{ fontSize: 12, fill: '#6b7280' }}
                      tickLine={false}
                      axisLine={{ strokeWidth: 1, stroke: '#e5e7eb' }}
                    />
                    <PolarRadiusAxis
                      angle={90}
                      domain={[0, 90]}
                      tick={{ fontSize: 10, fill: '#6b7280' }}
                      tickCount={6}
                      stroke="#e5e7eb"
                      axisLine={{ strokeWidth: 1, stroke: '#e5e7eb' }}
                    />
                    <Tooltip
                      formatter={(value: number, _name: string, props: any) => [
                        `${value.toLocaleString()} (${props.payload.percentage}%)`,
                        props.payload.name
                      ]}
                    />
                    <Radar
                      name="Score"
                      dataKey="score"
                      fill="#3b82f6"
                      fillOpacity={0.5}
                      animationDuration={1500}
                      animationBegin={300}
                    >
                      {radarChartData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.fill} />
                      ))}
                    </Radar>
                    <Legend
                      verticalAlign="bottom"
                      height={36}
                      iconType="circle"
                      iconSize={10}
                      wrapperStyle={{ paddingTop: 20 }}
                    />
                  </RadarChart>
                )}
              </ResponsiveContainer>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
