"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionV() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    management_approach: "",
    authority_delegation: "",
    decision_making_process: "",
    deputy_availability: "",
    transition_plan: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 5
    const sectionData = assessments.find(a => a.section_number === 5);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        management_approach: sectionData.management_approach || "",
        authority_delegation: sectionData.authority_delegation || "",
        decision_making_process: sectionData.decision_making_process || "",
        deputy_availability: sectionData.deputy_availability || "",
        transition_plan: sectionData.transition_plan || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(5, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  return (
    <QuestionCard
      title="FEDERATION MANAGEMENT"
      bengaliTitle="ফেডারেশনের ব্যবস্থাপনা"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess the Federation's management practices, decision-making processes, and operational sustainability.",
        bengali: "ফেডারেশনের ব্যবস্থাপনা পদ্ধতি, সিদ্ধান্ত গ্রহণের প্রক্রিয়া এবং পরিচালনাগত স্থায়িত্ব মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="How is the Federation managed?"
        bengaliQuestion="ফেডারেশন কীভাবে পরিচালিত হয়?"
        value={formData.management_approach}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, management_approach: value }))
        }
        options={[
          {
            value: "a",
            label: "It follows a developed annual plan.",
            bengaliLabel: "এটি একটি উন্নত বার্ষিক পরিকল্পনা অনুসরণ করে।"
          },
          {
            value: "b",
            label: "Our Federation does not develop a formal plan.",
            bengaliLabel: "আমাদের ফেডারেশন কোনো আনুষ্ঠানিক পরিকল্পনা তৈরি করে না।"
          },
          {
            value: "c",
            label: "It operates on an ad-hoc basis, addressing needs as they arise.",
            bengaliLabel: "এটি প্রয়োজন অনুযায়ী তাৎক্ষণিক ভিত্তিতে পরিচালিত হয়।"
          },
          {
            value: "d",
            label: "It is managed based on instructions from partner organizations or in response to national-level demands.",
            bengaliLabel: "এটি অংশীদার সংস্থার নির্দেশনা বা জাতীয় পর্যায়ের চাহিদার প্রতিক্রিয়ার ভিত্তিতে পরিচালিত হয়।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={2}
        question="Do the Federation's leadership delegate authority to its members when assigning tasks to members or subcommittees?"
        bengaliQuestion="ফেডারেশনের নেতৃত্ব কি সদস্যদের বা উপকমিটিগুলির মধ্যে কাজ বণ্টনের সময় তাদেরকে ক্ষমতা প্রদান করে?"
        value={formData.authority_delegation}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, authority_delegation: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, the Federation's leadership delegates authority to members and subcommittees when assigning tasks to ensure efficient decision-making and execution.",
            bengaliLabel: "হ্যাঁ, ফেডারেশনের নেতৃত্ব কাজ বন্টনের সময় সদস্য এবং উপকমিটিগুলিকে ক্ষমতা প্রদান করে যাতে কার্যকর সিদ্ধান্ত গ্রহণ এবং বাস্তবায়ন নিশ্চিত হয়।"
          },
          {
            value: "b",
            label: "No, the Federation's leadership retains full authority and only assigns tasks without granting any decision-making power to members or subcommittees.",
            bengaliLabel: "না, ফেডারেশনের নেতৃত্ব সম্পূর্ণ ক্ষমতা ধরে রাখে এবং কেবল কাজ বরাদ্দ করে, তবে সদস্যদের বা উপকমিটিগুলিকে কোনো সিদ্ধান্ত গ্রহণের ক্ষমতা প্রদান করে না।"
          },
          {
            value: "c",
            label: "The Leadership delegates partial authority allowing members or subcommittees to manage tasks but requiring final approval for major decisions.",
            bengaliLabel: "নেতৃত্ব আংশিক ক্ষমতা প্রদান করে, যা সদস্য বা উপকমিটিগুলিকে কাজ পরিচালনার সুযোগ দেয়, তবে বড় সিদ্ধান্তের জন্য চূড়ান্ত অনুমোদন প্রয়োজন।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={3}
        question="How are decisions made within the Federation?"
        bengaliQuestion="ফেডারেশনের মধ্যে কীভাবে সিদ্ধান্ত নেওয়া হয়?"
        value={formData.decision_making_process}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, decision_making_process: value }))
        }
        options={[
          {
            value: "1",
            label: "The President and/or Secretary make decisions alone.",
            bengaliLabel: "সভাপতি এবং অথবা সাধারণ সম্পাদক একাই সিদ্ধান্ত নেন।"
          },
          {
            value: "2",
            label: "The President and/or Secretary make decisions after consultation.",
            bengaliLabel: "সভাপতি এবং অথবা সাধারণ সম্পাদক পরামর্শের পরে সিদ্ধান্ত নেন।"
          },
          {
            value: "3",
            label: "Decisions are made through a democratic process by voting.",
            bengaliLabel: "গণতান্ত্রিক প্রক্রিয়ার মাধ্যমে ভোটের মাধ্যমে সিদ্ধান্ত নেওয়া হয়।"
          },
          {
            value: "4",
            label: "Everyone participates in decision-making, and all voices are heard.",
            bengaliLabel: "সকলেই সিদ্ধান্ত গ্রহণ প্রক্রিয়ায় অংশগ্রহণ করে, এবং সকলের মতামত শোনা হয়।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={4}
        question="Is there any Deputy or other member who can fulfill the duties of the President / Secretary?"
        bengaliQuestion="সভাপতি / সাধারণ সম্পাদকের দায়িত্ব পালনের জন্য কোনো সহ-সভাপতি বা অন্য সদস্য রয়েছেন কি?"
        value={formData.deputy_availability}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, deputy_availability: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, there is one person assigned to perform the duties of President/Secretary in absence of President/Secretary",
            bengaliLabel: "হ্যাঁ, সভাপতি / সাধারণ সম্পাদকের অনুপস্থিতিতে তাঁদের দায়িত্ব পালন করার জন্য একজন ব্যক্তি নিয়োজিত আছেন।"
          },
          {
            value: "b",
            label: "We assign a member sometimes and he used to carry out the tasks but not fully efficient yet",
            bengaliLabel: "আমরা কখনো কখনো একজন সদস্যকে নিযুক্ত করি এবং তিনি কাজগুলো সম্পাদন করেন, তবে এখনও পুরোপুরি দক্ষ নন।"
          },
          {
            value: "c",
            label: "No, there is no person who can deliver the tasks of the President / Secretary",
            bengaliLabel: "না, সভাপতি / সাধারণ সম্পাদকের কাজ সম্পাদন করার জন্য কেউ নেই।"
          }
        ]}
      />

      <RadioQuestion
        questionNumber={5}
        question="Is there a Plan for handling a transition process, including operations and continuation of the activities of the Federation?"
        bengaliQuestion="ফেডারেশনের কার্যক্রমের ধারাবাহিকতা এবং পরিচালনা বজায় রেখে রূপান্তর প্রক্রিয়া পরিচালনার জন্য কোনো পরিকল্পনা আছে কি?"
        value={formData.transition_plan}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, transition_plan: value }))
        }
        options={[
          {
            value: "a",
            label: "Yes, there is a plan developed to facilitate transition.",
            bengaliLabel: "হ্যাঁ, রূপান্তর প্রক্রিয়াকে সহজতর করার জন্য একটি পরিকল্পনা তৈরি করা হয়েছে।"
          },
          {
            value: "b",
            label: "Yes, we developed but was not required and not in practice",
            bengaliLabel: "হ্যাঁ, আমরা একটি পরিকল্পনা তৈরি করেছি, তবে প্রয়োজন হয়নি এবং এটি কার্যকর নয়।"
          },
          {
            value: "c",
            label: "No, there is no plan to handle the transition",
            bengaliLabel: "না, রূপান্তর প্রক্রিয়া পরিচালনার জন্য কোনো পরিকল্পনা নেই।"
          }
        ]}
      />
    </QuestionCard>
  );
}