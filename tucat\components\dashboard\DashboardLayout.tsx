"use client";

import { UserNav } from "@/components/UserNav";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { ProtectedRoute } from "@/components/ProtectedRoute";
import { FederationInfoCard } from "@/components/dashboard/FederationInfoCard";
import { CommitteeInfoCard } from "@/components/dashboard/CommitteeInfoCard";
import { AssessmentChartCard } from "@/components/dashboard/AssessmentChartCard";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ArrowLeft, LayoutDashboard, BarChart3, FileText } from "lucide-react";
import Link from "next/link";
import { useDashboardContext } from "./DashboardProvider";
import { Skeleton } from "@/components/ui/skeleton";
import { useAuth } from "@/components/AuthContext";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useState, useEffect } from "react";
import { AverageSectionScoresCard } from "./AverageSectionScoresCard";
import { YouthRepresentationCard } from "./YouthRepresentationCard";

export function DashboardLayout() {
  const { federation, isLoading, allFederations, loadAssessment } = useDashboardContext();
  const { user } = useAuth();
  const [selectedFederationId, setSelectedFederationId] = useState<string | undefined>(undefined);

  useEffect(() => {
    if (user?.email === "<EMAIL>" && allFederations.length > 0 && !selectedFederationId) {
      setSelectedFederationId(allFederations[0].id);
    }
  }, [user, allFederations, selectedFederationId]);

  useEffect(() => {
    if (selectedFederationId) {
      loadAssessment(selectedFederationId);
    }
  }, [selectedFederationId, loadAssessment]);

  const handleFederationChange = (value: string) => {
    setSelectedFederationId(value);
  };

  return (
    <ProtectedRoute>
      <div className="flex min-h-screen flex-col bg-background/50">
        <header className="sticky top-0 z-40 border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
          <div className="w-full px-4 md:px-6 flex h-16 items-center py-4">
            <div className="flex items-center gap-3">
              <Link href="/assessment">
                <Button variant="ghost" size="icon" className="rounded-full">
                  <ArrowLeft className="h-4 w-4" />
                </Button>
              </Link>
              <div className="flex flex-col">
                <h1 className="text-xl font-bold flex items-center gap-2">
                  <LayoutDashboard className="h-5 w-5 text-primary" />
                  Dashboard
                </h1>
                {user?.email === "<EMAIL>" && allFederations.length > 0 ? (
                  <Select onValueChange={handleFederationChange} value={selectedFederationId}>
                    <SelectTrigger className="w-[200px]">
                      <SelectValue placeholder="Select a Federation" />
                    </SelectTrigger>
                    <SelectContent>
                      {allFederations.map((fed) => (
                        <SelectItem key={fed.id} value={fed.id!}>
                          {fed.name}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                ) : (
                  federation?.name && (
                    <p className="text-sm text-muted-foreground">{federation.name}</p>
                  )
                )}
              </div>
            </div>
            <div className="ml-auto flex items-center gap-2 sm:gap-4">
              {user?.email === "<EMAIL>" && (
                <Link href="/all-federation">
                  <Button variant="outline" className="mr-2">Admin Dashboard</Button>
                </Link>
              )}
              <ThemeToggle />
              <UserNav />
            </div>
          </div>
        </header>

        <main className="flex-1 container py-6 px-4 md:px-6 max-w-7xl mx-auto">
          {isLoading ? (
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4">
              {[1, 2, 3, 4, 5, 6].map((i) => (
                <div key={i} className={`col-span-full ${i <= 2 ? 'xl:col-span-2' : 'md:col-span-1 xl:col-span-2'}`}>
                  <Skeleton className="h-[300px] w-full rounded-lg" />
                </div>
              ))}
            </div>
          ) : (
            <Tabs defaultValue="analytics" className="w-full">
              <div className="mb-6">
                <TabsList className="grid w-full grid-cols-2 md:w-auto">
                  <TabsTrigger value="details">
                    <FileText className="h-3.5 w-3.5 mr-2" />
                    Details
                  </TabsTrigger>
                  <TabsTrigger value="analytics">
                    <BarChart3 className="h-3.5 w-3.5 mr-2" />
                    Federation Analytics
                  </TabsTrigger>
                  <TabsTrigger value="average-scores">
                    <FileText className="h-3.5 w-3.5 mr-2" />
                    Average Section Scores
                  </TabsTrigger>
                  <TabsTrigger value="youth-representation">
                    <FileText className="h-3.5 w-3.5 mr-2" />
                    Youth Representation
                  </TabsTrigger>
                </TabsList>
              </div>

              <TabsContent value="details" className="mt-0">
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4 mb-6">
                  <div className="col-span-full md:col-span-1 xl:col-span-2">
                    <FederationInfoCard />
                  </div>
                  <div className="col-span-full md:col-span-1 xl:col-span-2">
                    <CommitteeInfoCard />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="analytics" className="mt-0">
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4">
                  <div className="col-span-full">
                    <AssessmentChartCard />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="average-scores" className="mt-0">
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4">
                  <div className="col-span-full">
                    <AverageSectionScoresCard />
                  </div>
                </div>
              </TabsContent>

              <TabsContent value="youth-representation" className="mt-0">
                <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-4">
                  <div className="col-span-full">
                    <YouthRepresentationCard />
                  </div>
                </div>
              </TabsContent>
            </Tabs>
          )}
        </main>
      </div>
    </ProtectedRoute>
  );
}