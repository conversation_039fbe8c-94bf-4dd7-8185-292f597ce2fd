"use client";

import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";

interface TextQuestionProps {
  questionNumber: number;
  question: string;
  bengaliQuestion: string;
  value: string;
  onChange: (value: string) => void;
  maxWidth?: string;
}

export function TextQuestion({
  questionNumber,
  question,
  bengaliQuestion,
  value,
  onChange,
  maxWidth = "xl",
}: TextQuestionProps) {
  return (
    <div className="space-y-4">
      <Label className="text-base">
        {questionNumber}. {question}
        <span className="block text-sm text-muted-foreground mt-1">
          {bengaliQuestion}
        </span>
      </Label>
      <Input
        className={`max-w-${maxWidth}`}
        value={value}
        onChange={(e) => onChange(e.target.value)}
      />
    </div>
  );
}