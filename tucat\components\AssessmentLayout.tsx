"use client";

import React, { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  CheckCircle2, MenuIcon, PanelLeftClose, PanelLeftOpen, Calculator,
  ChevronRight, Save, FileText, Users, Building, Settings,
  BarChart, BookOpen, Briefcase, HelpCircle, Info
} from "lucide-react";
import { useAssessmentContext } from "./AssessmentContext";
import { Result } from "./Result";
import { UserNav } from "./UserNav";
import { ProtectedRoute } from "./ProtectedRoute";
import { UserRole } from "@/lib/types";
import { ThemeToggle } from "@/components/ui/theme-toggle";
import { Progress } from "@/components/ui/progress";
import { AssessmentBreadcrumb } from "./AssessmentBreadcrumb";
import { toast } from "sonner";
import { Accordion, Accordion<PERSON>ontent, Accordion<PERSON><PERSON>, AccordionTrigger } from "@/components/ui/accordion";
import { HoverCard, HoverCardContent, HoverCardTrigger } from "@/components/ui/hover-card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Card } from "@/components/ui/card";

export const sections = [
  {
    id: 1,
    title: "Introduction",
    bengali: "ফেডারেশন পরিচিতি",
    isComplete: false,
    description: "Basic information about your federation",
    category: "general"
  },
  {
    id: 2,
    title: "Legal Status and Compliance",
    bengali: "আইনি অবস্থা এবং প্রতিপালন",
    isComplete: false,
    description: "Legal registration and compliance with regulations",
    category: "governance"
  },
  {
    id: 3,
    title: "Leadership",
    bengali: "নেতৃত্ব এবং শাসন",
    isComplete: false,
    description: "Leadership structure and governance practices",
    category: "governance"
  },
  {
    id: 4,
    title: "Organizational Structure",
    bengali: "ফেডারেশনের সংগঠনিক কাঠামো",
    isComplete: false,
    description: "How your federation is structured and organized",
    category: "governance"
  },
  {
    id: 5,
    title: "Management",
    bengali: "ফেডারেশনের ব্যবস্থাপনা",
    isComplete: false,
    description: "Day-to-day management and operations",
    category: "operations"
  },
  {
    id: 6,
    title: "Worker Participation",
    bengali: "শ্রমিকদের অংশগ্রহণ",
    isComplete: false,
    description: "How workers participate in decision-making",
    category: "operations"
  },
  {
    id: 7,
    title: "Culture and Gender",
    bengali: "সংস্কৃতি এবং লিঙ্গ",
    isComplete: false,
    description: "Cultural practices and gender equality",
    category: "operations"
  },
  {
    id: 8,
    title: "Collective Bargaining",
    bengali: "যৌথ দরকষাকষি এবং অ্যাডভোকেসি",
    isComplete: false,
    description: "Advocacy and collective bargaining activities",
    category: "engagement"
  },
  {
    id: 9,
    title: "Member Engagement",
    bengali: "সদস্যদের সম্পৃক্ততা এবং যোগাযোগ",
    isComplete: false,
    description: "Communication and engagement with members",
    category: "engagement"
  },
  {
    id: 10,
    title: "Financial Stability",
    bengali: "আর্থিক এবং সাংগঠনিক স্থিতিশীলতা",
    isComplete: false,
    description: "Financial management and sustainability",
    category: "sustainability"
  },
  {
    id: 11,
    title: "Audit & Compliance",
    bengali: "অডিট",
    isComplete: false,
    description: "Audit procedures and regulatory compliance",
    category: "sustainability"
  },
];

// Helper function to get the icon for each category
const getCategoryIcon = (category: string) => {
  switch (category) {
    case 'general':
      return <Info className="h-4 w-4" />;
    case 'governance':
      return <Building className="h-4 w-4" />;
    case 'operations':
      return <Settings className="h-4 w-4" />;
    case 'engagement':
      return <Users className="h-4 w-4" />;
    case 'sustainability':
      return <BarChart className="h-4 w-4" />;
    default:
      return <FileText className="h-4 w-4" />;
  }
};

// Helper function to get the category title
const getCategoryTitle = (category: string) => {
  switch (category) {
    case 'general':
      return 'General Information';
    case 'governance':
      return 'Governance & Leadership';
    case 'operations':
      return 'Operations & Management';
    case 'engagement':
      return 'Member Engagement';
    case 'sustainability':
      return 'Financial Sustainability';
    default:
      return 'Other';
  }
};

export function AssessmentLayout({
  children
}: {
  children: React.ReactNode
}) {
  const [sidebarOpen, setSidebarOpen] = useState(true);
  const [showResults, setShowResults] = useState(false);
  const { currentSection, setCurrentSection, assessments, calculateScore, federation } = useAssessmentContext();
  const [progressPercentage, setProgressPercentage] = useState(0);
  const [activeCategory, setActiveCategory] = useState<string | null>(null);

  // Calculate initial progress percentage based on assessments length
  // This will be updated in the useEffect below
  useEffect(() => {
    const completedSections = new Set(assessments.map(a => a.section_number));

    // Special handling for Section I - consider it complete if federation data exists
    const isSectionIComplete = federation && federation.id && Object.keys(federation).length > 0;

    sections.forEach((section, index) => {
      // For Section I, check if federation data exists
      if (section.id === 1) {
        sections[index].isComplete = isSectionIComplete || false;
      } else {
        // For other sections, check if they exist in the assessments array
        sections[index].isComplete = completedSections.has(section.id);
      }
    });

    // Calculate progress based on completed sections count
    const completedSectionsCount = sections.filter(section => section.isComplete).length;
    const updatedProgressPercentage = Math.round((completedSectionsCount / sections.length) * 100);
    setProgressPercentage(updatedProgressPercentage);
  }, [assessments, federation]);

  // Set active category based on current section
  useEffect(() => {
    const currentSectionData = sections.find(section => section.id === currentSection);
    if (currentSectionData) {
      setActiveCategory(currentSectionData.category);
    }
  }, [currentSection]);

  const selectSection = (sectionId: number) => {
    setCurrentSection(sectionId);
  };

  const handleCalculateScore = () => {
    calculateScore();
    setShowResults(true);
  };

  return (
    <ProtectedRoute allowedRoles={[UserRole.ADMIN, UserRole.FEDERATION_ADMIN, UserRole.FEDERATION_MEMBER]}>
      <div className="flex min-h-screen bg-background">
        {/* Sidebar */}
        <aside
          className={cn(
            "fixed inset-y-0 left-0 z-50 transform border-r bg-card transition-all duration-200 ease-in-out md:sticky md:top-0",
            sidebarOpen ? "translate-x-0 w-72" : "md:translate-x-0 md:w-16 -translate-x-full",
            "flex flex-col shadow-lg md:shadow-none"
          )}
        >
          <div className="flex h-16 items-center justify-between border-b px-4">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="icon"
                className="mr-3"
                onClick={() => setSidebarOpen(!sidebarOpen)}
              >
                {sidebarOpen ? (
                  <PanelLeftClose className="h-4 w-4" />
                ) : (
                  <PanelLeftOpen className="h-4 w-4" />
                )}
              </Button>
              <h2 className={cn("text-lg font-semibold transition-opacity",
                sidebarOpen ? "opacity-100" : "opacity-0"
              )}>
                Assessment
              </h2>
            </div>
            {sidebarOpen && <UserNav />}
          </div>

          <ScrollArea className="h-[calc(100vh-4rem)]">
            {sidebarOpen ? (
              <div className="p-4 space-y-6">
                {/* Progress summary card */}
                <Card className="p-4 bg-muted/50 border-muted">
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm font-medium">Progress</span>
                      <Badge variant={progressPercentage === 100 ? "success" : "outline"} className="text-xs">
                        {progressPercentage}%
                      </Badge>
                    </div>
                    <Progress value={progressPercentage} className="h-2" />
                    <p className="text-xs text-muted-foreground mt-2">
                      {sections.filter(s => s.isComplete).length} of {sections.length} sections completed
                    </p>
                  </div>
                </Card>

                {/* Sections grouped by category */}
                <Accordion
                  type="multiple"
                  defaultValue={['general', 'governance', 'operations', 'engagement', 'sustainability']}
                  className="space-y-2"
                >
                  {/* Group sections by category */}
                  {Object.entries(sections.reduce((acc, section) => {
                    const category = section.category;
                    if (!acc[category]) acc[category] = [];
                    acc[category].push(section);
                    return acc;
                  }, {} as Record<string, typeof sections>)).map(([category, categorySections]) => (
                    <AccordionItem
                      key={category}
                      value={category}
                      className={cn(
                        "border rounded-lg overflow-hidden",
                        activeCategory === category ? "border-primary/50 bg-muted/30" : "border-border/50"
                      )}
                    >
                      <AccordionTrigger className="px-4 py-3 hover:bg-muted/50 transition-colors">
                        <div className="flex items-center gap-2">
                          {getCategoryIcon(category)}
                          <span className="font-medium">{getCategoryTitle(category)}</span>
                        </div>
                      </AccordionTrigger>
                      <AccordionContent className="px-2 pb-2 pt-0">
                        <div className="space-y-1 mt-1">
                          {categorySections.map((section) => (
                            <HoverCard key={section.id} openDelay={300} closeDelay={100}>
                              <HoverCardTrigger asChild>
                                <div
                                  onClick={() => selectSection(section.id)}
                                  className={cn(
                                    "group relative rounded-md transition-all duration-200 flex w-full cursor-pointer items-center justify-between px-3 py-2",
                                    currentSection === section.id
                                      ? "bg-accent text-accent-foreground"
                                      : "hover:bg-muted/80",
                                    section.isComplete && "border-l-2 border-l-green-500"
                                  )}
                                >
                                  <div className="flex items-center gap-3">
                                    <div className={cn(
                                      "flex h-6 w-6 items-center justify-center rounded-full text-xs font-medium",
                                      currentSection === section.id
                                        ? "bg-primary text-primary-foreground"
                                        : "bg-muted text-muted-foreground"
                                    )}>
                                      {section.id}
                                    </div>
                                    <div className="flex flex-col">
                                      <span className="text-sm font-medium">{section.title}</span>
                                      <span className="text-xs text-muted-foreground">{section.bengali}</span>
                                    </div>
                                  </div>
                                  {section.isComplete ? (
                                    <CheckCircle2 className="h-4 w-4 text-green-500 shrink-0" />
                                  ) : (
                                    <div className="h-4 w-4 rounded-full border border-muted-foreground/30 shrink-0"></div>
                                  )}
                                </div>
                              </HoverCardTrigger>
                              <HoverCardContent side="right" align="start" className="w-80 p-4">
                                <div className="space-y-2">
                                  <div className="flex items-center gap-2">
                                    <div className={cn(
                                      "flex h-6 w-6 items-center justify-center rounded-full text-xs font-medium",
                                      "bg-primary text-primary-foreground"
                                    )}>
                                      {section.id}
                                    </div>
                                    <h4 className="font-semibold">{section.title}</h4>
                                  </div>
                                  <p className="text-sm">{section.description}</p>
                                  <div className="pt-2">
                                    <Badge variant={section.isComplete ? "success" : "outline"} className="text-xs">
                                      {section.isComplete ? "Completed" : "Not completed"}
                                    </Badge>
                                  </div>
                                </div>
                              </HoverCardContent>
                            </HoverCard>
                        ))}
                      </div>
                    </AccordionContent>
                  </AccordionItem>
                ))}
                </Accordion>
              </div>
            ) : (
              <div className="py-4 flex flex-col items-center space-y-4">
                {/* Collapsed sidebar with just section numbers */}
                {sections.map((section) => (
                  <div
                    key={section.id}
                    onClick={() => selectSection(section.id)}
                    className={cn(
                      "relative flex h-8 w-8 cursor-pointer items-center justify-center rounded-full",
                      currentSection === section.id
                        ? "bg-primary text-primary-foreground"
                        : "bg-muted hover:bg-muted/80 text-muted-foreground",
                      section.isComplete && "ring-1 ring-green-500"
                    )}
                    title={section.title}
                  >
                    <span className="text-xs font-medium">{section.id}</span>
                    {section.isComplete && (
                      <div className="absolute -top-1 -right-1 h-3 w-3 rounded-full bg-green-500 border-2 border-background"></div>
                    )}
                  </div>
                ))}
              </div>
            )}
          </ScrollArea>
        </aside>

      {/* Main content */}
      <div className="flex-1">
        <div className="sticky top-0 z-40 flex flex-col border-b bg-background">
          <div className="flex h-16 items-center px-4">
          <Button
            variant="ghost"
            size="icon"
            className="mr-2 md:hidden"
            onClick={() => setSidebarOpen(!sidebarOpen)}
          >
            <MenuIcon className="h-6 w-6" />
          </Button>
          <div className="flex items-center space-x-2">
            <h1 className="text-xl font-semibold">Trade Union Assessment</h1>
            <span className="text-sm text-muted-foreground">
              {showResults ? "Results" : `Section ${currentSection === 1 ? "I" : currentSection}`}
            </span>
          </div>
          <div className="ml-auto flex items-center gap-2">
            <ThemeToggle />
            <Button
              onClick={handleCalculateScore}
              disabled={assessments.length === 0}
              className="flex items-center gap-2"
            >
              <Calculator className="w-4 h-4" />
              Calculate Score
            </Button>
            <Button
              variant="outline"
              className="flex items-center gap-2"
              asChild
            >
              <a href="/dashboard">Dashboard</a>
            </Button>
          </div>
          </div>
          {/* Progress bar */}
          <div className="px-4 pb-2">
            <div className="flex items-center justify-between mb-1 text-xs">
              <span>Assessment Progress</span>
              <span>{progressPercentage}%</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </div>
        <main className="container mx-auto p-4 md:p-6 lg:p-8 transition-all duration-200 ease-in-out">
          {!showResults && <AssessmentBreadcrumb />}
          {showResults ? (
            <Result />
          ) : (
            <>
              {React.Children.toArray(children)[currentSection - 1]}

              {/* Floating navigation buttons */}
              <div className="fixed bottom-6 right-6 flex gap-2 z-50">
                {currentSection > 1 && (
                  <Button
                    variant="outline"
                    size="sm"
                    className="shadow-md hover:shadow-lg transition-all duration-200 bg-background/80 backdrop-blur-sm"
                    onClick={() => setCurrentSection(currentSection - 1)}
                  >
                    <ChevronRight className="h-4 w-4 rotate-180 mr-1" />
                    Previous
                  </Button>
                )}
                {currentSection < sections.length && (
                  <Button
                    variant="default"
                    size="sm"
                    className="shadow-md hover:shadow-lg transition-all duration-200 bg-primary/90 backdrop-blur-sm"
                    onClick={() => {
                      // Special handling for Section I
                      if (currentSection === 1) {
                        if (federation && federation.id) {
                          setCurrentSection(currentSection + 1);
                          // Scroll to top when navigating to next section
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                        } else {
                          toast.warning("Please complete and save Section I first");
                        }
                      } else {
                        // For other sections, check assessments
                        const isCompleted = assessments.some(a => a.section_number === currentSection);
                        if (isCompleted) {
                          setCurrentSection(currentSection + 1);
                          // Scroll to top when navigating to next section
                          window.scrollTo({ top: 0, behavior: 'smooth' });
                        } else {
                          toast.warning("Please complete and save this section first");
                        }
                      }
                    }}
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                )}
              </div>
            </>
          )}
        </main>
      </div>
    </div>
    </ProtectedRoute>
  );
}