"use client";

import { useState, useEffect } from "react";
import { QuestionCard } from "@/components/questions/QuestionCard";
import { RadioQuestion } from "@/components/questions/RadioQuestion";
import { useAssessmentContext } from "@/components/AssessmentContext";
import { toast } from "sonner";

export function SectionVII() {
  const { saveAssessment, isLoading, federation, assessments } = useAssessmentContext();
  const [formData, setFormData] = useState({
    cultural_gender_consideration: "",
    consider_local_culture: "",
    documented_guidelines: "",
    provide_training: "",
    use_assessment_findings: "",
  });

  // Load saved assessment data when component mounts
  useEffect(() => {
    // Find assessment data for section 7
    const sectionData = assessments.find(a => a.section_number === 7);
    if (sectionData) {
      // Update form data with saved values
      setFormData({
        cultural_gender_consideration: sectionData.cultural_gender_consideration || "",
        consider_local_culture: sectionData.consider_local_culture || "",
        documented_guidelines: sectionData.documented_guidelines || "",
        provide_training: sectionData.provide_training || "",
        use_assessment_findings: sectionData.use_assessment_findings || "",
      });
    }
  }, [assessments]);

  const handleSave = async () => {
    try {
      if (!federation.id) {
        toast.error("Please complete Section I first");
        return;
      }

      // Check if all questions are answered
      const unansweredFields = Object.entries(formData).filter(([_, value]) => !value);
      if (unansweredFields.length > 0) {
        toast.error("Please answer all questions before saving.");
        return;
      }

      await saveAssessment(7, formData);
      toast.success("Section data saved successfully");
    } catch (error) {
      console.error("Failed to save:", error);
      toast.error("Failed to save section data");
    }
  };

  const handleYesNoChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <QuestionCard
      title="CULTURE AND GENDER"
      bengaliTitle="সংস্কৃতি এবং লিঙ্গ"
      showAlert={!federation.id}
      onSave={handleSave}
      isLoading={isLoading}
      isDisabled={!federation.id}
      objective={{
        english: "To assess how the federation integrates cultural and gender considerations into its programs and operations.",
        bengali: "ফেডারেশন কীভাবে তার কর্মসূচি এবং কার্যক্রমে সাংস্কৃতিক এবং জেন্ডার বিষয়গুলি বিবেচনায় নিয়ে একীভূত করে তা মূল্যায়ন করা।"
      }}
    >
      <RadioQuestion
        questionNumber={1}
        question="How does the Federation handle cultural and gender considerations?"
        bengaliQuestion="ফেডারেশন কীভাবে সাংস্কৃতিক এবং জেন্ডার বিষয়গুলো বিবেচনায় নিয়ে পরিচালনা করে?"
        value={formData.cultural_gender_consideration}
        onChange={(value) =>
          setFormData((prev) => ({ ...prev, cultural_gender_consideration: value }))
        }
        options={[
          {
            value: "1",
            label: "The Federation does not consider local cultural or gender issues in its programming and does not have tools for assessing these issues",
            bengaliLabel: "প্রোগ্রামিংয়ে স্থানীয় সংস্কৃতি বা লিঙ্গ বিবেচনাকে বিবেচনা করে না এবং স্থানীয় সংস্কৃতি বা লিঙ্গ বিবেচনা মূল্যায়নের জন্য কোনো উপকরণ নেই।"
          },
          {
            value: "2",
            label: "The Federation considers cultural/gender issues but lacks assessment tools and only discusses these with staff",
            bengaliLabel: "সাংস্কৃতিক/লিঙ্গ বিষয়গুলি বিবেচনা করে কিন্তু মূল্যায়ন উপকরণ নেই এবং শুধুমাত্র কর্মীদের সাথে এ নিয়ে আলোচনা করে।"
          },
          {
            value: "3",
            label: "The Federation has assessment tools and guidelines but does not provide training on their use",
            bengaliLabel: "ফেডারেশনের মূল্যায়ন উপকরণ এবং নির্দেশিকা আছে কিন্তু এগুলি ব্যবহারের প্রশিক্ষণ দেয় না।"
          },
          {
            value: "4",
            label: "The Federation fully integrates cultural and gender considerations with tools, guidelines, and staff training",
            bengaliLabel: "ফেডারেশন উপকরণ, নির্দেশিকা এবং কর্মী প্রশিক্ষণের মাধ্যমে সাংস্কৃতিক এবং লিঙ্গ বিবেচনাকে সম্পূর্ণরূপে একীভূত করে।"
          }
        ]}
      />

      <div className="border rounded-lg overflow-hidden bg-blue-50/50">
        <div className="p-4 border-b">
          <div className="font-semibold">Culture and Gender Assessment</div>
          <div className="text-sm">সংস্কৃতি এবং লিঙ্গ মূল্যায়ন</div>
        </div>
        <table className="w-full border-collapse">
          <thead className="bg-muted/50">
            <tr className="border-b">
              <th className="p-3 text-left">#</th>
              <th className="p-3 text-left">Question</th>
              <th className="p-3 text-center w-24">Yes</th>
              <th className="p-3 text-center w-24">No</th>
              <th className="p-3 text-center w-24">N/A</th>
            </tr>
          </thead>
          <tbody>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">2.</td>
              <td className="p-3 border-r">
                <div>Does your Federation consider local culture and gender in programming?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি প্রোগ্রামিংয়ে স্থানীয় সংস্কৃতি এবং লিঙ্গ বিবেচনা করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="consider_local_culture"
                      value="yes"
                      checked={formData.consider_local_culture === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, consider_local_culture: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="consider_local_culture"
                      value="no"
                      checked={formData.consider_local_culture === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, consider_local_culture: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="consider_local_culture"
                      value="na"
                      checked={formData.consider_local_culture === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, consider_local_culture: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">3.</td>
              <td className="p-3 border-r">
                <div>Does your Federation have clearly documented guidelines for culturally relevant and/or gender-based approaches and programming?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি সাংস্কৃতিকভাবে প্রাসঙ্গিক এবং/অথবা লিঙ্গ-ভিত্তিক পদ্ধতি এবং প্রোগ্রামিংয়ের জন্য সুস্পষ্টভাবে নথিভুক্ত নির্দেশিকা রয়েছে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="documented_guidelines"
                      value="yes"
                      checked={formData.documented_guidelines === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, documented_guidelines: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="documented_guidelines"
                      value="no"
                      checked={formData.documented_guidelines === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, documented_guidelines: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="documented_guidelines"
                      value="na"
                      checked={formData.documented_guidelines === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, documented_guidelines: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">4.</td>
              <td className="p-3 border-r">
                <div>Does your Federation provide training in gender and/or cultural issues and survey tools?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি লিঙ্গ এবং/অথবা সাংস্কৃতিক বিষয় এবং জরিপের সরঞ্জাম নিয়ে প্রশিক্ষণ প্রদান করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="provide_training"
                      value="yes"
                      checked={formData.provide_training === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, provide_training: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="provide_training"
                      value="no"
                      checked={formData.provide_training === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, provide_training: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="provide_training"
                      value="na"
                      checked={formData.provide_training === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, provide_training: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
            <tr className="border-b">
              <td className="p-3 w-12 align-top">5.</td>
              <td className="p-3 border-r">
                <div>Are findings from culture and/or gender assessments used in program development and implementation?</div>
                <div className="text-sm text-muted-foreground">
                  আপনার ফেডারেশন কি সংস্কৃতি এবং/অথবা লিঙ্গ মূল্যায়নের ফলাফলগুলো প্রোগ্রাম উন্নয়ন এবং বাস্তবায়নে ব্যবহার করে?
                </div>
              </td>
              <td colSpan={3} className="p-3">
                <div className="flex justify-center gap-12">
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="use_assessment_findings"
                      value="yes"
                      checked={formData.use_assessment_findings === "yes"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, use_assessment_findings: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="use_assessment_findings"
                      value="no"
                      checked={formData.use_assessment_findings === "no"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, use_assessment_findings: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>No</span>
                  </label>
                  <label className="flex items-center gap-2 cursor-pointer">
                    <input
                      type="radio"
                      name="use_assessment_findings"
                      value="na"
                      checked={formData.use_assessment_findings === "na"}
                      onChange={(e) =>
                        setFormData((prev) => ({ ...prev, use_assessment_findings: e.target.value }))
                      }
                      className="w-4 h-4 text-primary border-gray-300 focus:ring-primary"
                    />
                    <span>N/A</span>
                  </label>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </QuestionCard>
  );
}